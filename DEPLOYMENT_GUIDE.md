# 🚀 Liz Farm Website - TrueHost Deployment Guide

## 🇰🇪 TrueHost Kenya Deployment

This guide is specifically designed for deploying the Liz Farm website to TrueHost, Kenya's leading web hosting provider. TrueHost offers excellent Python support and cPanel management.

## 🎯 Quick Start for TrueHost

### Prerequisites:
- ✅ TrueHost hosting account with Python support
- ✅ Domain name configured
- ✅ cPanel access credentials
- ✅ All project files ready for upload

### 🚀 Express Deployment (5 Minutes)

1. **Access cPanel** → File Manager → Navigate to your domain folder
2. **Upload Core Files**: `app.py`, `wsgi.py`, `requirements.txt`, `.htaccess`
3. **Create Templates Folder** → Upload all HTML templates
4. **Setup Python App** → cPanel → Python App → Create Application
5. **Install Dependencies** → Terminal: `pip install -r requirements.txt`
6. **Configure Environment** → Set `FLASK_ENV=production` and `SECRET_KEY`
7. **Test Your Site** → Visit your domain

**Your site should now be live!** 🎉

---

## 📋 Pre-Deployment Checklist

### Essential Files to Upload:
- ✅ `app.py` (main Flask application - 484 lines)
- ✅ `wsgi.py` (WSGI entry point for production)
- ✅ `requirements.txt` (Python dependencies)
- ✅ `.htaccess` (Apache configuration)

### Template Files (Upload entire `templates/` folder):
#### Main Pages:
- ✅ `templates/index.html` (Homepage with stats, services, testimonials)
- ✅ `templates/services.html` (Responsive services page)

#### Admin Templates:
- ✅ `templates/admin/login.html` (Admin login page)
- ✅ `templates/admin/dashboard.html` (Admin dashboard)
- ✅ `templates/admin/services.html` (Manage services)
- ✅ `templates/admin/add_service.html` (Add new service)
- ✅ `templates/admin/edit_service.html` (Edit existing service)
- ✅ `templates/admin/stats.html` (Manage homepage statistics)
- ✅ `templates/admin/edit_stat.html` (Edit statistics)
- ✅ `templates/admin/testimonials.html` (Manage testimonials)
- ✅ `templates/admin/add_testimonial.html` (Add new testimonial)
- ✅ `templates/admin/edit_testimonial.html` (Edit testimonial)
- ✅ `templates/admin/settings.html` (WhatsApp & general settings)

### Optional Helper Files:
- 📄 `backup_data.py` (Backup your data before upload)
- 📄 `setup_production.py` (Test production setup)
- 📄 `DEPLOYMENT_GUIDE.md` (This guide)

## 📱 **Responsive Design Features**

Your Liz Farm website is fully responsive and mobile-optimized:

### **Mobile Navigation:**
- **Hamburger Menu**: Clean 3-line menu icon on mobile devices
- **Slide-out Menu**: Smooth navigation with proper animations
- **Touch-Friendly**: Large touch targets for easy mobile use
- **Logo Visibility**: Company logo and branding visible on all screen sizes
- **Cross Icon**: Menu button transforms to X when opened

### **Responsive Services Page:**
- **Adaptive Grid**: 1 column on mobile, 2 columns on desktop
- **Flexible Cards**: Service cards resize beautifully across devices
- **Mobile-First Buttons**: Stacked buttons on small screens, inline on desktop
- **Optimized Text**: Font sizes scale appropriately (text-sm to text-xl)
- **Touch Targets**: All buttons sized for easy mobile interaction

### **Homepage Responsive Features:**
- **Hero Section**: Text scales from mobile to desktop (text-3xl to text-7xl)
- **Stats Animation**: Works smoothly on all devices
- **Testimonials**: Grid adapts from 1 to 3 columns
- **Services Preview**: Responsive card layout
- **Contact Form**: Mobile-optimized form fields

### **Cross-Device Compatibility:**
- **Mobile Phones**: 320px - 768px (fully optimized)
- **Tablets**: 768px - 1024px (responsive layout)
- **Desktop**: 1024px+ (full desktop layout)
- **Large Screens**: 1440px+ (enhanced spacing and typography)

### **Performance Optimizations:**
- **Mobile-First CSS**: Faster loading on mobile devices
- **Optimized Images**: Responsive image sizing
- **Touch Gestures**: Swipe and tap optimizations
- **Fast Animations**: Hardware-accelerated transitions

## 🔧 TrueHost / cPanel Deployment Process

### 1. **Access Your TrueHost Control Panel**
- Log in to your TrueHost account
- Go to cPanel or your hosting control panel
- Look for "File Manager" or "Files" section

### 2. **Navigate to Your Domain Folder**
- **Main Domain**: `public_html/`
- **Addon Domain**: `public_html/yourdomain.com/`
- **Subdomain**: `public_html/subdomain/`
- **Example**: If your domain is `lizfarm.co.ke`, go to `public_html/lizfarm.co.ke/`

### 3. **Upload Your Files**

#### **Option A: Using cPanel File Manager (Recommended for beginners)**
1. Click "Upload" in File Manager
2. **Upload Core Files** (to your domain root folder):
   - `app.py` (main application)
   - `wsgi.py` (production entry point)
   - `requirements.txt` (dependencies)
   - `.htaccess` (Apache config)
3. **Create Templates Folder**:
   - Click "New Folder" → Name it `templates`
   - Enter the `templates` folder
4. **Upload Template Files**:
   - Upload `index.html` and `services.html` to `templates/`
   - Create `admin` subfolder inside `templates/`
   - Upload all admin HTML files to `templates/admin/`

#### **Option B: Using FTP Client (FileZilla) - Faster for multiple files**
- **Host**: `ftp.yourdomain.com` or your server IP
- **Username**: Your cPanel username
- **Password**: Your cPanel password
- **Port**: 21 (FTP) or 22 (SFTP)
- **Upload Structure**:
  ```
  public_html/yourdomain.com/
  ├── app.py
  ├── wsgi.py
  ├── requirements.txt
  ├── .htaccess
  └── templates/
      ├── index.html
      ├── services.html
      └── admin/
          ├── login.html
          ├── dashboard.html
          ├── services.html
          ├── add_service.html
          ├── edit_service.html
          ├── stats.html
          ├── edit_stat.html
          ├── testimonials.html
          ├── add_testimonial.html
          ├── edit_testimonial.html
          └── settings.html
  ```

### 4. **Set Up Python Environment**

#### **Step 4.1: Create Python Application**
1. In cPanel, find "Python App" or "Setup Python App"
2. Click "Create Application"
3. **Configure Application**:
   - **Python Version**: 3.9 or higher (recommended: 3.11 if available)
   - **Application Root**: `/public_html/yourdomain.com/` (your domain folder)
   - **Application URL**: `yourdomain.com` (your actual domain)
   - **Application Startup File**: `wsgi.py`
   - **Application Entry Point**: `application`

#### **Step 4.2: Important TrueHost Specific Settings**
- **Passenger**: Usually enabled by default
- **Static Files**: Leave empty (handled by Flask)
- **Environment Variables**: Set up in next step

### 5. **Install Dependencies**
- In the Python App section, find "Terminal" or "Console"
- Run: `pip install -r requirements.txt`
- Wait for all packages to install

### 6. **Configure Environment Variables**

**Essential Environment Variables for Production:**

1. **In Python App settings, add these variables:**
   - `FLASK_ENV`: `production`
   - `SECRET_KEY`: Generate a secure random key (see below)
   - `PORT`: Usually `5000` or as specified by host

2. **Generate a Secure Secret Key:**
   ```python
   # Run this in Python terminal to generate a secure key
   import secrets
   print(secrets.token_hex(32))
   ```

3. **Optional Environment Variables:**
   - `MAIL_SERVER`: `smtp.gmail.com` (if using email features)
   - `MAIL_PORT`: `587`
   - `MAIL_USE_TLS`: `True`
   - `MAIL_USERNAME`: Your email address
   - `MAIL_PASSWORD`: Your app-specific password
   - `ADMIN_EMAIL`: `<EMAIL>`

4. **Security Best Practices:**
   - ✅ Never use the default secret key in production
   - ✅ Use strong, unique passwords for admin accounts
   - ✅ Enable two-factor authentication on your hosting account
   - ✅ Regularly update your secret keys
   - ✅ Monitor access logs for suspicious activity

### 7. **Set File Permissions**
- Set folder permissions to `755`
- Set file permissions to `644`
- Make sure `wsgi.py` is executable (`755`)

## 🔍 Testing Your Deployment

### 1. **Basic Site Functionality**
- **Homepage Test**: Visit `https://yourdomain.com`
  - ✅ Liz Farm homepage loads
  - ✅ Navigation menu works (mobile & desktop)
  - ✅ Stats animation plays (500+, 250+, 8+, 1200+)
  - ✅ Services section displays all 4 services
  - ✅ Testimonials show with animations (no photos, just icons)
  - ✅ WhatsApp buttons work on all services
  - ✅ Contact form submits successfully

- **Services Page Test**: Visit `https://yourdomain.com/services`
  - ✅ Responsive design works on mobile/tablet/desktop
  - ✅ Service cards display properly
  - ✅ WhatsApp integration works
  - ✅ "Learn More" expandable sections work
  - ✅ Mobile navigation menu slides in from left

### 2. **Responsive Design Testing**
- **Mobile Testing** (320px - 768px):
  - ✅ Hamburger menu appears and works
  - ✅ Logo and company name visible
  - ✅ Service cards stack vertically
  - ✅ Buttons are touch-friendly
  - ✅ Text is readable without zooming
  - ✅ WhatsApp buttons work properly

- **Tablet Testing** (768px - 1024px):
  - ✅ Navigation adapts to tablet size
  - ✅ Service cards show 2 columns
  - ✅ Touch targets are appropriate
  - ✅ Text scaling looks good

- **Desktop Testing** (1024px+):
  - ✅ Full navigation menu visible
  - ✅ Service cards in proper grid
  - ✅ All hover effects work
  - ✅ Typography scales appropriately

### 3. **Admin Panel Testing**
- **Access**: Go to `https://yourdomain.com/admin`
- **Default Login**: `admin` / `admin123`
- **⚠️ CRITICAL**: Change admin password immediately!

#### **Admin Features to Test**:
- ✅ **Dashboard**: Shows overview of services, stats, testimonials
- ✅ **Services Management**: Add, edit, delete services
- ✅ **Statistics Management**: Update homepage stats with live preview
- ✅ **Testimonials Management**: Add, edit, delete testimonials (with icons)
- ✅ **Settings**: Configure WhatsApp number
- ✅ **All forms work**: No 500 errors when submitting

### 3. **Mobile Responsiveness Test**
- Test on actual mobile devices or browser dev tools
- ✅ **Navigation**: Mobile menu slides in from left
- ✅ **Services Page**: Cards stack properly on mobile
- ✅ **Buttons**: WhatsApp buttons work on mobile
- ✅ **Forms**: Admin forms work on mobile devices
- ✅ **Text**: All text is readable without horizontal scrolling

## ⚠️ Important Security Steps

### 1. **Change Admin Credentials**
- Log into admin panel
- Change username and password
- Use strong, unique credentials

### 2. **Update Secret Key**
- Generate a new secret key for production
- Add it to environment variables

### 3. **Enable HTTPS**
- TrueHost usually provides free SSL certificates
- Enable SSL in cPanel
- Update any hardcoded HTTP links to HTTPS

## 🛠️ Troubleshooting Common Issues

### **Issue 1: 500 Internal Server Error**
**Symptoms**: White page with "Internal Server Error"
**Solutions**:
- ✅ Check Python app is running in cPanel
- ✅ Verify `wsgi.py` exists and has correct permissions (755)
- ✅ Check error logs: cPanel → Error Logs
- ✅ Ensure Python version is 3.9+ (recommended: 3.11)
- ✅ Verify all dependencies installed: `pip list`
- ✅ Check file structure matches the upload guide
- ✅ Restart Python application in cPanel
- ✅ Check if gunicorn is installed: `pip show gunicorn`

### **Issue 2: Admin Panel Not Working**
**Symptoms**: 404 error on `/admin` or login not working
**Solutions**:
- ✅ Verify all admin template files uploaded to `templates/admin/`
- ✅ Check `app.py` uploaded correctly
- ✅ Test with default credentials: `admin` / `admin123`
- ✅ Clear browser cache and cookies

### **Issue 3: Services Page Not Responsive**
**Symptoms**: Mobile layout broken or not responsive
**Solutions**:
- ✅ Ensure `services.html` uploaded with latest responsive code
- ✅ Test on actual mobile device, not just browser resize
- ✅ Check Alpine.js is loading (for mobile menu)
- ✅ Verify Tailwind CSS is loading from CDN

### **Issue 4: WhatsApp Buttons Not Working**
**Symptoms**: WhatsApp buttons don't open WhatsApp
**Solutions**:
- ✅ Update WhatsApp number in admin settings
- ✅ Ensure number format includes country code (+254...)
- ✅ Test on mobile device (WhatsApp app installed)
- ✅ Check JavaScript console for errors

### **Issue 5: Stats Animation Not Working**
**Symptoms**: Stats show 0+ instead of actual values
**Solutions**:
- ✅ Restart Python application in cPanel
- ✅ Check browser JavaScript console for errors
- ✅ Verify stats data in admin panel
- ✅ Clear browser cache

### **Issue 6: Python Dependencies Not Installing**
**Symptoms**: Import errors or missing modules
**Solutions**:
- ✅ Use Python app terminal: `pip install -r requirements.txt`
- ✅ Try: `pip install --user -r requirements.txt`
- ✅ Check Python version compatibility
- ✅ Contact TrueHost support for Python environment issues

### **Issue 7: File Upload Problems**
**Symptoms**: Files not uploading or corrupted
**Solutions**:
- ✅ Use FTP client instead of web file manager
- ✅ Check file size limits in cPanel
- ✅ Ensure proper file permissions after upload
- ✅ Verify folder structure is correct

## 📱 Mobile Testing Tips

### **Browser Developer Tools:**
1. **Chrome**: Press F12 → Click device icon → Select mobile device
2. **Firefox**: Press F12 → Click responsive design mode
3. **Safari**: Develop menu → Enter Responsive Design Mode

### **Real Device Testing:**
- **iPhone**: Test Safari and Chrome browsers
- **Android**: Test Chrome and Samsung Internet
- **iPad**: Test both portrait and landscape modes

### **Key Mobile Checks:**
- ✅ Menu hamburger icon appears below 768px width
- ✅ Logo remains visible and properly sized
- ✅ All buttons are at least 44px tall (Apple guideline)
- ✅ Text is readable without horizontal scrolling
- ✅ WhatsApp buttons open WhatsApp app correctly
- ✅ Forms are easy to fill on mobile keyboards

## 📞 TrueHost Specific Tips & Features

### **TrueHost Python Support (2024 Update)**
- ✅ **Python Versions**: 3.9, 3.10, 3.11, 3.12 available
- ✅ **Passenger WSGI**: Automatically configured for Flask apps
- ✅ **Package Installation**: pip works in Python app terminal
- ✅ **Environment Variables**: Set through Python app interface
- ✅ **Auto-restart**: App restarts automatically on file changes
- ✅ **Resource Limits**: Generous CPU and memory allocation
- ✅ **SSL Certificates**: Free Let's Encrypt SSL included
- ✅ **Backup Services**: Daily automated backups available

### **TrueHost cPanel Features**
- 🔧 **Python App Manager**: Easy setup and management
- 📁 **File Manager**: Web-based file upload and editing
- 📊 **Error Logs**: Real-time error monitoring
- 🔒 **SSL Certificates**: Free Let's Encrypt SSL
- 📧 **Email Accounts**: Create professional email addresses
- 📈 **Resource Usage**: Monitor CPU, memory, and bandwidth

### **Performance Optimization**
- ⚡ **CDN Integration**: Use TrueHost's CDN for faster loading
- 🗜️ **Gzip Compression**: Enabled via .htaccess
- 📱 **Mobile Optimization**: Responsive design implemented
- 🖼️ **Image Optimization**: Use WebP format when possible
- ⚡ **Caching**: Browser caching configured in .htaccess

### **TrueHost Support Channels**
- 💬 **Live Chat**: Available 24/7 in client area
- 🎫 **Support Tickets**: Submit through client portal
- 📞 **Phone Support**: Available during business hours
- 📚 **Knowledge Base**: Comprehensive documentation
- 🎥 **Video Tutorials**: Step-by-step guides

### **What to Tell TrueHost Support**
When contacting support, provide:
- 🔍 **Issue Description**: "Python Flask application deployment"
- 📝 **Error Messages**: Copy exact error text
- 🌐 **Domain Name**: Your specific domain
- 📂 **File Structure**: Mention you're using WSGI with Flask
- 🐍 **Python Version**: Specify version you're using

### **TrueHost Hosting Plans Compatibility**
- ✅ **Shared Hosting**: Works with Python app support
- ✅ **VPS Hosting**: Full control and better performance
- ✅ **Dedicated Servers**: Maximum performance and resources
- ⚠️ **Basic Plans**: Check if Python support is included

## 🔄 Post-Deployment Maintenance

### **Regular Updates**
- Update testimonials through admin panel
- Modify services as needed
- Update statistics regularly

### **Backup**
- Regularly backup your files
- Export testimonials/services data
- Keep local copies of templates

### **Monitoring**
- Check site regularly for issues
- Monitor error logs
- Test contact forms periodically

## 📱 Contact Information Update

Don't forget to update:
- WhatsApp number in admin settings
- Contact information in templates
- Social media links in footer
- Email addresses for contact forms

## 🎉 Go Live Checklist

- [ ] All files uploaded successfully
- [ ] Python app configured and running
- [ ] Dependencies installed
- [ ] Admin credentials changed
- [ ] SSL certificate enabled
- [ ] Contact information updated
- [ ] All pages tested and working
- [ ] WhatsApp integration configured
- [ ] Testimonials displaying correctly
- [ ] Stats animation working

Your Liz Farm website should now be live and fully functional on TrueHost hosting!

For additional support, contact TrueHost customer service or refer to their Python hosting documentation.
