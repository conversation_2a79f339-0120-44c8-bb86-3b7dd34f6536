#!/usr/bin/env python3
"""
Liz Farm Website - Development Server
Run this script to start the development server
"""

import os
import sys
from app import app

def create_database():
    """Create database tables if they don't exist"""
    print("✅ Running in demo mode - no database required!")
    return True

def check_environment():
    """Check if environment is properly configured"""
    required_vars = ['SECRET_KEY']
    missing_vars = []
    
    for var in required_vars:
        if not os.environ.get(var) and not app.config.get(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"⚠️  Warning: Missing environment variables: {', '.join(missing_vars)}")
        print("   Using default values for development.")
    
    return True

def main():
    """Main function to start the application"""
    print("🌿 Starting Liz Farm - The Oasis Website...")
    print("=" * 50)
    
    # Check environment
    if not check_environment():
        sys.exit(1)
    
    # Create database
    if not create_database():
        sys.exit(1)
    
    # Start the development server
    print("🚀 Starting development server...")
    print("📱 Website will be available at: http://localhost:5000")
    print("🛑 Press Ctrl+C to stop the server")
    print("=" * 50)
    
    try:
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=True,
            use_reloader=True
        )
    except KeyboardInterrupt:
        print("\n👋 Server stopped. Goodbye!")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
