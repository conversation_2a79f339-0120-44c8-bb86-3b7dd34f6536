<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Liz Farm</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'farm-green': {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#22c55e',
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-br from-farm-green-400 to-farm-green-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-seedling text-white"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-900">Liz Farm Admin</h1>
                        <p class="text-sm text-gray-600">Dashboard</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="{{ url_for('index') }}" class="text-gray-600 hover:text-farm-green-600 transition-colors">
                        <i class="fas fa-external-link-alt mr-1"></i>
                        View Website
                    </a>
                    <a href="{{ url_for('admin_logout') }}" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-sign-out-alt mr-1"></i>
                        Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="mb-6 p-4 rounded-lg {% if category == 'error' %}bg-red-50 text-red-800 border border-red-200{% else %}bg-green-50 text-green-800 border border-green-200{% endif %}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl p-6 shadow-lg">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-farm-green-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-cogs text-farm-green-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Total Services</p>
                        <p class="text-2xl font-bold text-gray-900">{{ services|length }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-lg">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-money-bill-wave text-blue-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Revenue (KES)</p>
                        <p class="text-2xl font-bold text-gray-900">
                            {{ "{:,}".format(services|sum(attribute='price')) }}
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-lg">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-users text-purple-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Active Programs</p>
                        <p class="text-2xl font-bold text-gray-900">{{ services|length }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-lg">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                        <i class="fab fa-whatsapp text-green-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">WhatsApp</p>
                        <p class="text-lg font-bold text-gray-900">{{ whatsapp_number }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6 mb-8">
            <a href="{{ url_for('admin_services') }}" class="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow group">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-farm-green-100 rounded-full flex items-center justify-center group-hover:bg-farm-green-200 transition-colors">
                        <i class="fas fa-cogs text-farm-green-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-900">Manage Services</h3>
                        <p class="text-sm text-gray-600">Add, edit, or delete services</p>
                    </div>
                </div>
            </a>

            <a href="{{ url_for('admin_stats') }}" class="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow group">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center group-hover:bg-orange-200 transition-colors">
                        <i class="fas fa-chart-bar text-orange-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-900">Manage Statistics</h3>
                        <p class="text-sm text-gray-600">Update homepage stats</p>
                    </div>
                </div>
            </a>

            <a href="{{ url_for('admin_add_service') }}" class="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow group">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center group-hover:bg-blue-200 transition-colors">
                        <i class="fas fa-plus text-blue-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-900">Add New Service</h3>
                        <p class="text-sm text-gray-600">Create a new service offering</p>
                    </div>
                </div>
            </a>

            <a href="{{ url_for('admin_testimonials') }}" class="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow group">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center group-hover:bg-yellow-200 transition-colors">
                        <i class="fas fa-quote-left text-yellow-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-900">Testimonials</h3>
                        <p class="text-sm text-gray-600">Manage customer reviews</p>
                    </div>
                </div>
            </a>

            <a href="{{ url_for('admin_settings') }}" class="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow group">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center group-hover:bg-indigo-200 transition-colors">
                        <i class="fas fa-cog text-indigo-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-900">Settings</h3>
                        <p class="text-sm text-gray-600">Configure WhatsApp & more</p>
                    </div>
                </div>
            </a>

            <a href="{{ url_for('index') }}" class="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow group">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center group-hover:bg-purple-200 transition-colors">
                        <i class="fas fa-eye text-purple-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-900">View Website</h3>
                        <p class="text-sm text-gray-600">See how services appear to visitors</p>
                    </div>
                </div>
            </a>
        </div>

        <!-- Recent Services -->
        <div class="bg-white rounded-xl shadow-lg">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-900">Recent Services</h2>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    {% for service in services[:5] %}
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-{{ service.color }}-100 rounded-full flex items-center justify-center">
                                <i class="{{ service.icon }} text-{{ service.color }}-600"></i>
                            </div>
                            <div class="ml-4">
                                <h3 class="font-semibold text-gray-900">{{ service.title }}</h3>
                                <p class="text-sm text-gray-600">{{ service.subtitle }}</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold text-gray-900">KES {{ "{:,}".format(service.price) }}</p>
                            <p class="text-sm text-gray-600">{{ service.duration }}</p>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</body>
</html>
