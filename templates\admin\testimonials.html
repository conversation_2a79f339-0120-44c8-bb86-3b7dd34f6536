<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Testimonials - <PERSON> Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'farm-green': {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#22c55e',
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-br from-farm-green-400 to-farm-green-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-seedling text-white"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-900">Liz Farm Admin</h1>
                        <p class="text-sm text-gray-600">Testimonials Management</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="{{ url_for('admin_dashboard') }}" class="text-gray-600 hover:text-farm-green-600 transition-colors">
                        <i class="fas fa-dashboard mr-1"></i>
                        Dashboard
                    </a>
                    <a href="{{ url_for('admin_logout') }}" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-sign-out-alt mr-1"></i>
                        Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="mb-6 p-4 rounded-lg {% if category == 'error' %}bg-red-50 text-red-800 border border-red-200{% else %}bg-green-50 text-green-800 border border-green-200{% endif %}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Manage Testimonials</h1>
                <p class="text-gray-600 mt-2">Add, edit, and manage customer testimonials displayed on your website</p>
            </div>
            <a href="{{ url_for('admin_add_testimonial') }}" 
               class="bg-farm-green-500 hover:bg-farm-green-600 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105">
                <i class="fas fa-plus mr-2"></i>
                Add New Testimonial
            </a>
        </div>

        <!-- Testimonials Grid -->
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {% for testimonial in testimonials %}
            <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                <!-- Header -->
                <div class="p-6 border-b border-gray-200">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            {% for i in range(testimonial.rating) %}
                            <i class="fas fa-star text-yellow-400 text-sm mr-1"></i>
                            {% endfor %}
                            <span class="text-sm text-gray-500 ml-2">({{ testimonial.rating }}/5)</span>
                        </div>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-farm-green-100 text-farm-green-800">
                            {{ testimonial.service }}
                        </span>
                    </div>
                    
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-{{ testimonial.color }}-100 rounded-full flex items-center justify-center mr-4 border-2 border-{{ testimonial.color }}-200">
                            <i class="{{ testimonial.icon }} text-{{ testimonial.color }}-600 text-lg"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-900">{{ testimonial.name }}</h3>
                            <p class="text-sm text-gray-600">{{ testimonial.role }}</p>
                            <p class="text-xs text-gray-500">{{ testimonial.location }}</p>
                        </div>
                    </div>
                </div>

                <!-- Content -->
                <div class="p-6">
                    <blockquote class="text-gray-700 text-sm leading-relaxed mb-4">
                        "{{ testimonial.text[:100] }}{% if testimonial.text|length > 100 %}...{% endif %}"
                    </blockquote>
                    
                    <div class="text-xs text-gray-500 mb-4">
                        <i class="fas fa-calendar mr-1"></i>
                        {{ testimonial.date }}
                    </div>

                    <!-- Actions -->
                    <div class="flex space-x-2">
                        <a href="{{ url_for('admin_edit_testimonial', testimonial_id=testimonial.id) }}" 
                           class="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg text-sm font-medium transition-colors text-center">
                            <i class="fas fa-edit mr-1"></i>
                            Edit
                        </a>
                        <form method="POST" action="{{ url_for('admin_delete_testimonial', testimonial_id=testimonial.id) }}" 
                              onsubmit="return confirm('Are you sure you want to delete this testimonial?')" class="flex-1">
                            <button type="submit" 
                                    class="w-full bg-red-500 hover:bg-red-600 text-white py-2 px-4 rounded-lg text-sm font-medium transition-colors">
                                <i class="fas fa-trash mr-1"></i>
                                Delete
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        {% if not testimonials %}
        <div class="text-center py-12">
            <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-quote-left text-gray-400 text-3xl"></i>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">No Testimonials Yet</h3>
            <p class="text-gray-600 mb-6">Start building trust by adding customer testimonials to your website.</p>
            <a href="{{ url_for('admin_add_testimonial') }}" 
               class="bg-farm-green-500 hover:bg-farm-green-600 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105">
                <i class="fas fa-plus mr-2"></i>
                Add Your First Testimonial
            </a>
        </div>
        {% endif %}

        <!-- Instructions -->
        <div class="mt-8 bg-blue-50 rounded-xl p-6">
            <div class="flex items-start">
                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-4 mt-1">
                    <i class="fas fa-info text-blue-600"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-blue-900 mb-2">Testimonials Best Practices</h3>
                    <ul class="text-blue-800 space-y-1 text-sm">
                        <li>• Use real customer photos and names for authenticity</li>
                        <li>• Keep testimonials specific and detailed about results achieved</li>
                        <li>• Include location and service type for credibility</li>
                        <li>• Mix testimonials from different services and locations</li>
                        <li>• Update testimonials regularly with fresh content</li>
                        <li>• Use high-quality images (recommended: 150x150px, square format)</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
