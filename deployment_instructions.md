# 🌿 Liz Farm - TrueHost Deployment Guide

## 📁 Files to Upload to TrueHost

### **Required Files:**
1. `app.py` - Main Flask application
2. `wsgi.py` - WSGI configuration for production
3. `requirements.txt` - Python dependencies
4. `.htaccess` - Apache configuration
5. `templates/` folder - All HTML templates
6. `static/` folder - CSS, JS, images

### **Step 1: Access TrueHost cPanel**
1. Go to your TrueHost cPanel
2. Look for "File Manager" in the Files section
3. Click "File Manager"

### **Step 2: Navigate to Your Domain Folder**
1. Go to `public_html/` folder
2. Create a new folder called `lizfarm/`
3. Enter the `lizfarm/` folder

### **Step 3: Upload Files**
Upload these files to `public_html/lizfarm/`:

#### **Main Application Files:**
- `app.py`
- `wsgi.py` 
- `requirements.txt`
- `.htaccess`

#### **Folders to Upload:**
- `templates/` (entire folder with all HTML files)
- `static/` (entire folder with CSS, JS, images)

### **Step 4: Set Up Python Environment**
1. In cPanel, look for "Python App" or "Python Selector"
2. Create a new Python application:
   - **Python Version**: 3.6 or higher
   - **Application Root**: `/public_html/lizfarm`
   - **Application URL**: `yourdomain.com/lizfarm`
   - **Application Startup File**: `wsgi.py`

### **Step 5: Install Dependencies**
1. In the Python App interface, open "Terminal" or "Console"
2. Run: `pip install -r requirements.txt`

### **Step 6: Configure Application**
1. Make sure `wsgi.py` points to your Flask app
2. Set environment variables if needed
3. Test the application

### **Step 7: Access Your Website**
Your Liz Farm website will be available at:
`https://astrabyte.africa/lizfarm`

## 🔧 Troubleshooting

### **Common Issues:**
1. **500 Error**: Check file permissions (755 for folders, 644 for files)
2. **Module Not Found**: Ensure all dependencies are installed
3. **Template Not Found**: Verify templates folder is uploaded correctly

### **File Permissions:**
- Folders: 755
- Python files: 644
- .htaccess: 644

## 📞 Support
If you encounter issues, contact TrueHost support with:
- Error messages
- File structure screenshot
- Python app configuration details
