#!/usr/bin/env python3
"""
Simple test script to verify the Flask app works
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app import app
    print("✅ Flask app imported successfully!")
    
    # Test if the app can be created
    with app.test_client() as client:
        # Test the main route
        response = client.get('/')
        print(f"✅ Main route status: {response.status_code}")
        
        # Test API endpoints
        test_data = {
            'name': 'Test User',
            'email': '<EMAIL>',
            'subject': 'Test Subject',
            'message': 'Test message'
        }
        
        response = client.post('/api/contact', 
                             json=test_data,
                             content_type='application/json')
        print(f"✅ Contact API status: {response.status_code}")
        
        # Test newsletter subscription
        response = client.post('/api/subscribe',
                             json={'email': '<EMAIL>'},
                             content_type='application/json')
        print(f"✅ Newsletter API status: {response.status_code}")
        
    print("🎉 All tests passed! The Flask app is working correctly.")
    print("🚀 You can now run the app with: python app.py")
    
except Exception as e:
    print(f"❌ Error testing Flask app: {e}")
    import traceback
    traceback.print_exc()
