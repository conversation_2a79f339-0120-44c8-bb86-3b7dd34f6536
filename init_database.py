#!/usr/bin/env python3
"""
Database initialization script for Liz Farm
Run this script to create database tables on TrueHost
"""

import os
import sys
from app import app, db

def init_database():
    """Initialize the database with all tables"""
    print("🌿 Initializing Liz Farm Database...")
    print("=" * 50)
    
    try:
        with app.app_context():
            # Create all tables
            db.create_all()
            print("✅ Database tables created successfully!")
            
            # Print table information
            print("\n📊 Created Tables:")
            print("- Contact (contact form submissions)")
            print("- JobApplication (job applications)")
            print("- TourBooking (tour bookings)")
            print("- NewsletterSubscription (newsletter subscriptions)")
            
            print("\n🎉 Database initialization complete!")
            print("Your Liz Farm website is ready to store data!")
            
    except Exception as e:
        print(f"❌ Error initializing database: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Check your DATABASE_URL environment variable")
        print("2. Ensure PostgreSQL database exists")
        print("3. Verify database credentials")
        return False
    
    return True

if __name__ == "__main__":
    init_database()
