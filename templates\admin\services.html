<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Services - Liz Farm Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'farm-green': {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#22c55e',
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-br from-farm-green-400 to-farm-green-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-seedling text-white"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-900">Liz Farm Admin</h1>
                        <p class="text-sm text-gray-600">Manage Services</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="{{ url_for('admin_dashboard') }}" class="text-gray-600 hover:text-farm-green-600 transition-colors">
                        <i class="fas fa-dashboard mr-1"></i>
                        Dashboard
                    </a>
                    <a href="{{ url_for('admin_logout') }}" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-sign-out-alt mr-1"></i>
                        Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="mb-6 p-4 rounded-lg {% if category == 'error' %}bg-red-50 text-red-800 border border-red-200{% else %}bg-green-50 text-green-800 border border-green-200{% endif %}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Manage Services</h1>
                <p class="text-gray-600 mt-2">Add, edit, or delete your service offerings</p>
            </div>
            <a href="{{ url_for('admin_add_service') }}" 
               class="bg-farm-green-500 hover:bg-farm-green-600 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105">
                <i class="fas fa-plus mr-2"></i>
                Add New Service
            </a>
        </div>

        <!-- Services Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {% for service in services %}
            <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                <div class="p-6">
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-{{ service.color }}-100 rounded-full flex items-center justify-center">
                                <i class="{{ service.icon }} text-{{ service.color }}-600 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-xl font-bold text-gray-900">{{ service.title }}</h3>
                                <p class="text-{{ service.color }}-600 font-medium">{{ service.subtitle }}</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="text-2xl font-bold text-gray-900">
                                {% if service.price > 0 %}
                                    KES {{ "{:,}".format(service.price) }}
                                {% else %}
                                    Free
                                {% endif %}
                            </p>
                            <p class="text-sm text-gray-600">{{ service.duration }}</p>
                        </div>
                    </div>

                    <p class="text-gray-600 mb-4 line-clamp-3">{{ service.description }}</p>

                    <div class="mb-4">
                        <h4 class="font-semibold text-gray-900 mb-2">Features:</h4>
                        <ul class="space-y-1">
                            {% for feature in service.features[:3] %}
                            <li class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-check-circle text-{{ service.color }}-500 mr-2"></i>
                                {{ feature }}
                            </li>
                            {% endfor %}
                            {% if service.features|length > 3 %}
                            <li class="text-sm text-gray-500">
                                ... and {{ service.features|length - 3 }} more
                            </li>
                            {% endif %}
                        </ul>
                    </div>

                    <div class="bg-{{ service.color }}-50 rounded-lg p-3 mb-4">
                        <p class="text-sm text-{{ service.color }}-800 font-medium">
                            <i class="fas fa-certificate mr-2"></i>
                            {{ service.certification }}
                        </p>
                    </div>

                    <div class="flex space-x-3">
                        <a href="{{ url_for('admin_edit_service', service_id=service.id) }}" 
                           class="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg font-medium transition-colors text-center">
                            <i class="fas fa-edit mr-1"></i>
                            Edit
                        </a>
                        <form method="POST" action="{{ url_for('admin_delete_service', service_id=service.id) }}" 
                              class="flex-1" onsubmit="return confirm('Are you sure you want to delete this service?')">
                            <button type="submit" 
                                    class="w-full bg-red-500 hover:bg-red-600 text-white py-2 px-4 rounded-lg font-medium transition-colors">
                                <i class="fas fa-trash mr-1"></i>
                                Delete
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        {% if not services %}
        <div class="text-center py-12">
            <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-cogs text-gray-400 text-3xl"></i>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">No Services Yet</h3>
            <p class="text-gray-600 mb-6">Get started by adding your first service offering</p>
            <a href="{{ url_for('admin_add_service') }}" 
               class="bg-farm-green-500 hover:bg-farm-green-600 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105">
                <i class="fas fa-plus mr-2"></i>
                Add Your First Service
            </a>
        </div>
        {% endif %}
    </div>

    <style>
        .line-clamp-3 {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
    </style>
</body>
</html>
