<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Testimonial - <PERSON> Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'farm-green': {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#22c55e',
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-br from-farm-green-400 to-farm-green-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-seedling text-white"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-900">Liz Farm Admin</h1>
                        <p class="text-sm text-gray-600">Add New Testimonial</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="{{ url_for('admin_testimonials') }}" class="text-gray-600 hover:text-farm-green-600 transition-colors">
                        <i class="fas fa-arrow-left mr-1"></i>
                        Back to Testimonials
                    </a>
                    <a href="{{ url_for('admin_logout') }}" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-sign-out-alt mr-1"></i>
                        Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="mb-6 p-4 rounded-lg {% if category == 'error' %}bg-red-50 text-red-800 border border-red-200{% else %}bg-green-50 text-green-800 border border-green-200{% endif %}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Add New Testimonial</h1>
            <p class="text-gray-600 mt-2">Create a new customer testimonial to display on your website</p>
        </div>

        <!-- Form -->
        <div class="bg-white rounded-xl shadow-lg" x-data="testimonialForm()">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-900">Testimonial Details</h2>
            </div>

            <form method="POST" class="p-6">
                <div class="grid md:grid-cols-2 gap-6">
                    <!-- Customer Information -->
                    <div class="space-y-6">
                        <h3 class="text-lg font-semibold text-gray-900">Customer Information</h3>
                        
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                                Customer Name *
                            </label>
                            <input type="text" 
                                   id="name" 
                                   name="name" 
                                   required
                                   x-model="form.name"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-farm-green-500 focus:border-farm-green-500 transition-colors"
                                   placeholder="e.g., Sarah Wanjiku">
                        </div>

                        <div>
                            <label for="role" class="block text-sm font-medium text-gray-700 mb-2">
                                Role/Title *
                            </label>
                            <input type="text" 
                                   id="role" 
                                   name="role" 
                                   required
                                   x-model="form.role"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-farm-green-500 focus:border-farm-green-500 transition-colors"
                                   placeholder="e.g., Agricultural Training Graduate">
                        </div>

                        <div>
                            <label for="location" class="block text-sm font-medium text-gray-700 mb-2">
                                Location *
                            </label>
                            <input type="text" 
                                   id="location" 
                                   name="location" 
                                   required
                                   x-model="form.location"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-farm-green-500 focus:border-farm-green-500 transition-colors"
                                   placeholder="e.g., Nairobi, Kenya">
                        </div>

                        <div>
                            <label for="icon" class="block text-sm font-medium text-gray-700 mb-2">
                                Icon *
                            </label>
                            <select id="icon"
                                    name="icon"
                                    required
                                    x-model="form.icon"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-farm-green-500 focus:border-farm-green-500 transition-colors">
                                <option value="fas fa-user">User</option>
                                <option value="fas fa-seedling">Seedling</option>
                                <option value="fas fa-briefcase">Briefcase</option>
                                <option value="fas fa-camera">Camera</option>
                                <option value="fas fa-handshake">Handshake</option>
                                <option value="fas fa-graduation-cap">Graduation Cap</option>
                                <option value="fas fa-globe">Globe</option>
                                <option value="fas fa-star">Star</option>
                                <option value="fas fa-heart">Heart</option>
                                <option value="fas fa-trophy">Trophy</option>
                            </select>
                        </div>

                        <div>
                            <label for="color" class="block text-sm font-medium text-gray-700 mb-2">
                                Color Theme *
                            </label>
                            <select id="color"
                                    name="color"
                                    required
                                    x-model="form.color"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-farm-green-500 focus:border-farm-green-500 transition-colors">
                                <option value="farm-green">Farm Green</option>
                                <option value="blue">Blue</option>
                                <option value="green">Green</option>
                                <option value="purple">Purple</option>
                                <option value="indigo">Indigo</option>
                                <option value="teal">Teal</option>
                                <option value="red">Red</option>
                                <option value="yellow">Yellow</option>
                                <option value="pink">Pink</option>
                                <option value="orange">Orange</option>
                            </select>
                        </div>
                    </div>

                    <!-- Testimonial Content -->
                    <div class="space-y-6">
                        <h3 class="text-lg font-semibold text-gray-900">Testimonial Content</h3>
                        
                        <div>
                            <label for="service" class="block text-sm font-medium text-gray-700 mb-2">
                                Service *
                            </label>
                            <select id="service" 
                                    name="service" 
                                    required
                                    x-model="form.service"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-farm-green-500 focus:border-farm-green-500 transition-colors">
                                <option value="">Select a service</option>
                                {% for service in services %}
                                <option value="{{ service.title }}">{{ service.title }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <div>
                            <label for="rating" class="block text-sm font-medium text-gray-700 mb-2">
                                Rating *
                            </label>
                            <select id="rating" 
                                    name="rating" 
                                    required
                                    x-model="form.rating"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-farm-green-500 focus:border-farm-green-500 transition-colors">
                                <option value="5">5 Stars - Excellent</option>
                                <option value="4">4 Stars - Very Good</option>
                                <option value="3">3 Stars - Good</option>
                                <option value="2">2 Stars - Fair</option>
                                <option value="1">1 Star - Poor</option>
                            </select>
                        </div>

                        <div>
                            <label for="date" class="block text-sm font-medium text-gray-700 mb-2">
                                Date *
                            </label>
                            <input type="date" 
                                   id="date" 
                                   name="date" 
                                   required
                                   x-model="form.date"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-farm-green-500 focus:border-farm-green-500 transition-colors">
                        </div>
                    </div>
                </div>

                <!-- Testimonial Text -->
                <div class="mt-6">
                    <label for="text" class="block text-sm font-medium text-gray-700 mb-2">
                        Testimonial Text *
                    </label>
                    <textarea id="text" 
                              name="text" 
                              rows="4" 
                              required
                              x-model="form.text"
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-farm-green-500 focus:border-farm-green-500 transition-colors"
                              placeholder="Write the customer's testimonial here..."></textarea>
                    <p class="text-sm text-gray-500 mt-1">Keep it authentic and specific about the results achieved</p>
                </div>

                <!-- Preview -->
                <div class="mt-8 p-6 bg-gray-50 rounded-lg" x-show="form.name && form.text">
                    <h3 class="font-semibold text-gray-900 mb-4">Preview</h3>
                    <div class="bg-white rounded-lg p-6 shadow">
                        <div class="flex items-center mb-4">
                            <template x-for="i in parseInt(form.rating || 5)">
                                <i class="fas fa-star text-yellow-400 text-lg mr-1"></i>
                            </template>
                            <span class="text-sm text-gray-500 ml-2" x-text="`(${form.rating || 5}/5)`"></span>
                        </div>
                        
                        <blockquote class="text-gray-700 mb-6 leading-relaxed" x-text="`\"${form.text}\"`"></blockquote>
                        
                        <div class="text-center">
                            <div class="w-12 h-12 rounded-full mx-auto mb-3 flex items-center justify-center"
                                 :class="`bg-${form.color || 'gray'}-100`">
                                <i :class="form.icon || 'fas fa-user'"
                                   :class="`text-${form.color || 'gray'}-600 text-lg`"></i>
                            </div>
                            <h4 class="font-semibold text-gray-900" x-text="form.name"></h4>
                            <p class="text-sm text-gray-600" x-text="form.role"></p>
                            <p class="text-xs text-gray-500" x-text="form.location"></p>
                        </div>
                        
                        <div class="mt-4 pt-4 border-t border-gray-100">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-farm-green-100 text-farm-green-800">
                                <i class="fas fa-check-circle mr-1"></i>
                                <span x-text="form.service"></span>
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200 mt-8">
                    <a href="{{ url_for('admin_testimonials') }}" 
                       class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="px-6 py-3 bg-farm-green-500 hover:bg-farm-green-600 text-white rounded-lg font-semibold transition-all duration-300 transform hover:scale-105">
                        <i class="fas fa-save mr-2"></i>
                        Add Testimonial
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function testimonialForm() {
            return {
                form: {
                    name: '',
                    role: '',
                    location: '',
                    icon: 'fas fa-user',
                    color: 'farm-green',
                    service: '',
                    rating: '5',
                    text: '',
                    date: new Date().toISOString().split('T')[0]
                }
            }
        }
    </script>
</body>
</html>
