#!/usr/bin/env python3
"""
WSGI entry point for Siltech website
This file is used by web servers like Apache or Nginx with mod_wsgi
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(__file__))

# Import the Flask application
from app import app

# This is what the WSGI server will call
application = app

if __name__ == "__main__":
    # For local development
    app.run(debug=False, host='0.0.0.0', port=5000)
