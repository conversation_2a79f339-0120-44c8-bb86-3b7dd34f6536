<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - Liz Farm Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'farm-green': {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#22c55e',
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-br from-farm-green-400 to-farm-green-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-seedling text-white"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-900">Liz Farm Admin</h1>
                        <p class="text-sm text-gray-600">Settings</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="{{ url_for('admin_dashboard') }}" class="text-gray-600 hover:text-farm-green-600 transition-colors">
                        <i class="fas fa-dashboard mr-1"></i>
                        Dashboard
                    </a>
                    <a href="{{ url_for('admin_logout') }}" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-sign-out-alt mr-1"></i>
                        Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="mb-6 p-4 rounded-lg {% if category == 'error' %}bg-red-50 text-red-800 border border-red-200{% else %}bg-green-50 text-green-800 border border-green-200{% endif %}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Website Settings</h1>
            <p class="text-gray-600 mt-2">Configure your website settings and integrations</p>
        </div>

        <!-- WhatsApp Settings -->
        <div class="bg-white rounded-xl shadow-lg">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4">
                        <i class="fab fa-whatsapp text-green-600 text-xl"></i>
                    </div>
                    <div>
                        <h2 class="text-xl font-bold text-gray-900">WhatsApp Integration</h2>
                        <p class="text-gray-600">Configure WhatsApp number for service inquiries</p>
                    </div>
                </div>
            </div>

            <form method="POST" class="p-6">
                <div class="space-y-6">
                    <div>
                        <label for="whatsapp_number" class="block text-sm font-medium text-gray-700 mb-2">
                            WhatsApp Number *
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fab fa-whatsapp text-green-500"></i>
                            </div>
                            <input type="tel" 
                                   id="whatsapp_number" 
                                   name="whatsapp_number" 
                                   value="{{ whatsapp_number }}" 
                                   required
                                   class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-farm-green-500 focus:border-farm-green-500 transition-colors"
                                   placeholder="+254712345678">
                        </div>
                        <p class="text-sm text-gray-500 mt-2">
                            Include country code (e.g., +254 for Kenya). This number will receive WhatsApp messages from service inquiries.
                        </p>
                    </div>

                    <!-- Preview -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="font-semibold text-gray-900 mb-3">Preview</h3>
                        <div class="bg-white rounded-lg p-4 border">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="font-medium text-gray-900">Agricultural Training</h4>
                                    <p class="text-sm text-gray-600">Sample service</p>
                                </div>
                                <div class="flex gap-2">
                                    <button type="button" class="bg-farm-green-500 text-white px-4 py-2 rounded-lg text-sm">
                                        Apply Now
                                    </button>
                                    <button type="button" class="bg-green-500 text-white px-3 py-2 rounded-lg">
                                        <i class="fab fa-whatsapp"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <p class="text-xs text-gray-500 mt-2">
                            This is how the WhatsApp button will appear on each service card
                        </p>
                    </div>

                    <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                        <a href="{{ url_for('admin_dashboard') }}" 
                           class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="px-6 py-3 bg-farm-green-500 hover:bg-farm-green-600 text-white rounded-lg font-semibold transition-all duration-300 transform hover:scale-105">
                            <i class="fas fa-save mr-2"></i>
                            Save Settings
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Instructions -->
        <div class="mt-8 bg-blue-50 rounded-xl p-6">
            <div class="flex items-start">
                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-4 mt-1">
                    <i class="fas fa-info text-blue-600"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-blue-900 mb-2">How WhatsApp Integration Works</h3>
                    <ul class="text-blue-800 space-y-1 text-sm">
                        <li>• Each service card will have a WhatsApp button alongside the main action button</li>
                        <li>• Clicking the WhatsApp button opens WhatsApp with a pre-filled message about that service</li>
                        <li>• Messages are sent directly to the WhatsApp number you configure here</li>
                        <li>• Make sure the number is active and can receive WhatsApp messages</li>
                        <li>• The number format should include the country code (e.g., +254 for Kenya)</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
