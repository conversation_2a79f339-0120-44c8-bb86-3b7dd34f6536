<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Statistic - Liz Farm Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'farm-green': {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#22c55e',
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-br from-farm-green-400 to-farm-green-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-seedling text-white"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-900">Liz Farm Admin</h1>
                        <p class="text-sm text-gray-600">Edit Statistic</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="{{ url_for('admin_stats') }}" class="text-gray-600 hover:text-farm-green-600 transition-colors">
                        <i class="fas fa-arrow-left mr-1"></i>
                        Back to Stats
                    </a>
                    <a href="{{ url_for('admin_logout') }}" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-sign-out-alt mr-1"></i>
                        Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="bg-white rounded-xl shadow-lg">
            <div class="p-6 border-b border-gray-200">
                <h1 class="text-2xl font-bold text-gray-900">Edit Statistic</h1>
                <p class="text-gray-600 mt-2">Update the details for "{{ stat.title }}"</p>
            </div>

            <form method="POST" class="p-6 space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Statistic Title *</label>
                        <input type="text" id="title" name="title" value="{{ stat.title }}" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-farm-green-500 focus:border-farm-green-500 transition-colors"
                               placeholder="e.g., Trained Farmers">
                    </div>

                    <div>
                        <label for="value" class="block text-sm font-medium text-gray-700 mb-2">Value *</label>
                        <input type="number" id="value" name="value" value="{{ stat.value }}" min="0" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-farm-green-500 focus:border-farm-green-500 transition-colors"
                               placeholder="e.g., 500">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="icon" class="block text-sm font-medium text-gray-700 mb-2">Icon Class *</label>
                        <select id="icon" name="icon" required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-farm-green-500 focus:border-farm-green-500 transition-colors">
                            <option value="fas fa-users" {% if stat.icon == 'fas fa-users' %}selected{% endif %}>👥 Users (People)</option>
                            <option value="fas fa-briefcase" {% if stat.icon == 'fas fa-briefcase' %}selected{% endif %}>💼 Briefcase (Jobs)</option>
                            <option value="fas fa-globe" {% if stat.icon == 'fas fa-globe' %}selected{% endif %}>🌍 Globe (Countries)</option>
                            <option value="fas fa-heart" {% if stat.icon == 'fas fa-heart' %}selected{% endif %}>❤️ Heart (Happy)</option>
                            <option value="fas fa-seedling" {% if stat.icon == 'fas fa-seedling' %}selected{% endif %}>🌱 Seedling (Growth)</option>
                            <option value="fas fa-trophy" {% if stat.icon == 'fas fa-trophy' %}selected{% endif %}>🏆 Trophy (Achievement)</option>
                            <option value="fas fa-star" {% if stat.icon == 'fas fa-star' %}selected{% endif %}>⭐ Star (Rating)</option>
                            <option value="fas fa-chart-line" {% if stat.icon == 'fas fa-chart-line' %}selected{% endif %}>📈 Chart (Growth)</option>
                            <option value="fas fa-handshake" {% if stat.icon == 'fas fa-handshake' %}selected{% endif %}>🤝 Handshake (Partnership)</option>
                            <option value="fas fa-graduation-cap" {% if stat.icon == 'fas fa-graduation-cap' %}selected{% endif %}>🎓 Graduation (Education)</option>
                        </select>
                    </div>

                    <div>
                        <label for="color" class="block text-sm font-medium text-gray-700 mb-2">Color Theme *</label>
                        <select id="color" name="color" required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-farm-green-500 focus:border-farm-green-500 transition-colors">
                            <option value="farm-green" {% if stat.color == 'farm-green' %}selected{% endif %}>🟢 Farm Green</option>
                            <option value="blue" {% if stat.color == 'blue' %}selected{% endif %}>🔵 Blue</option>
                            <option value="green" {% if stat.color == 'green' %}selected{% endif %}>🟢 Green</option>
                            <option value="purple" {% if stat.color == 'purple' %}selected{% endif %}>🟣 Purple</option>
                            <option value="red" {% if stat.color == 'red' %}selected{% endif %}>🔴 Red</option>
                            <option value="yellow" {% if stat.color == 'yellow' %}selected{% endif %}>🟡 Yellow</option>
                            <option value="indigo" {% if stat.color == 'indigo' %}selected{% endif %}>🟦 Indigo</option>
                            <option value="pink" {% if stat.color == 'pink' %}selected{% endif %}>🩷 Pink</option>
                            <option value="orange" {% if stat.color == 'orange' %}selected{% endif %}>🟠 Orange</option>
                            <option value="teal" {% if stat.color == 'teal' %}selected{% endif %}>🟢 Teal</option>
                        </select>
                    </div>
                </div>

                <!-- Preview -->
                <div class="bg-gray-50 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Preview</h3>
                    <div class="text-center p-4 bg-white rounded-lg">
                        <div class="flex items-center justify-center mb-3">
                            <div class="w-12 h-12 bg-{{ stat.color }}-100 rounded-full flex items-center justify-center">
                                <i class="{{ stat.icon }} text-{{ stat.color }}-600 text-xl"></i>
                            </div>
                        </div>
                        <div class="text-3xl font-bold text-{{ stat.color }}-600 mb-2">
                            {{ stat.value }}+
                        </div>
                        <p class="text-gray-600 font-medium">{{ stat.title }}</p>
                    </div>
                </div>

                <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                    <a href="{{ url_for('admin_stats') }}" 
                       class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="px-6 py-3 bg-farm-green-500 hover:bg-farm-green-600 text-white rounded-lg font-semibold transition-all duration-300 transform hover:scale-105">
                        <i class="fas fa-save mr-2"></i>
                        Update Statistic
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Live preview update
        document.addEventListener('DOMContentLoaded', function() {
            const titleInput = document.getElementById('title');
            const valueInput = document.getElementById('value');
            const iconSelect = document.getElementById('icon');
            const colorSelect = document.getElementById('color');
            
            const previewTitle = document.querySelector('.text-gray-600.font-medium');
            const previewValue = document.querySelector('.text-3xl.font-bold');
            const previewIcon = document.querySelector('.w-12.h-12 i');
            const previewIconBg = document.querySelector('.w-12.h-12');
            
            function updatePreview() {
                if (previewTitle) previewTitle.textContent = titleInput.value;
                if (previewValue) previewValue.textContent = valueInput.value + '+';
                
                if (previewIcon && iconSelect.value) {
                    previewIcon.className = iconSelect.value + ' text-' + colorSelect.value + '-600 text-xl';
                }
                
                if (previewIconBg) {
                    previewIconBg.className = 'w-12 h-12 bg-' + colorSelect.value + '-100 rounded-full flex items-center justify-center';
                }
                
                if (previewValue) {
                    previewValue.className = 'text-3xl font-bold text-' + colorSelect.value + '-600 mb-2';
                }
            }
            
            titleInput.addEventListener('input', updatePreview);
            valueInput.addEventListener('input', updatePreview);
            iconSelect.addEventListener('change', updatePreview);
            colorSelect.addEventListener('change', updatePreview);
        });
    </script>
</body>
</html>
