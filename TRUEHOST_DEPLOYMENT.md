# 🚀 TrueHost Deployment - Quick Start Guide

## 📋 **DEPLOYMENT CHECKLIST**

### **✅ Step 1: Access TrueHost cPanel**
1. Login to your TrueHost account
2. Go to cPanel
3. Find **"File Manager"** in the Files section
4. Click **"File Manager"**

### **✅ Step 2: Create Project Folder**
1. Navigate to `public_html/`
2. Create new folder: **`lizfarm`**
3. Enter the `lizfarm/` folder

### **✅ Step 3: Upload These Files**
Upload to `public_html/lizfarm/`:

**📁 Main Files:**
- ✅ `app.py`
- ✅ `wsgi.py`
- ✅ `requirements.txt`
- ✅ `.htaccess`

**📁 Folders:**
- ✅ `templates/` (entire folder)
- ✅ `static/` (entire folder)

### **✅ Step 4: Set Up Python Application**
1. In cPanel, find **"Python App"** or **"Setup Python App"**
2. Click **"Create Application"**
3. Configure:
   - **Python Version**: `3.6+`
   - **Application Root**: `/public_html/lizfarm`
   - **Application URL**: Leave blank or `lizfarm`
   - **Application Startup File**: `wsgi.py`
   - **Application Entry Point**: `application`

### **✅ Step 5: Install Dependencies**
1. In Python App interface, click **"Open Terminal"**
2. Run command: `pip install -r requirements.txt`
3. Wait for installation to complete

### **✅ Step 6: Set Up PostgreSQL Database**
1. In cPanel, find **"PostgreSQL Databases"**
2. Create new database: `vxivdkko_lizfarm`
3. Create database user: `vxivdkko_lizfarm`
4. Set password: `LizFarm2024!`
5. Add user to database with ALL PRIVILEGES

### **✅ Step 7: Initialize Database**
1. In Python App terminal, run: `python init_database.py`
2. Wait for "Database initialization complete!" message
3. This creates all necessary tables

### **✅ Step 8: Set File Permissions**
Set these permissions in File Manager:
- **Folders**: `755`
- **Python files**: `644`
- **`.htaccess`**: `644`

### **✅ Step 9: Test Your Website**
Visit: `https://astrabyte.africa/lizfarm`

## 🔧 **TROUBLESHOOTING**

### **❌ 500 Internal Server Error**
- Check file permissions
- Verify `wsgi.py` is correct
- Check error logs in cPanel

### **❌ Module Not Found Error**
- Ensure `requirements.txt` is uploaded
- Run `pip install -r requirements.txt` again
- Check Python version compatibility

### **❌ Template Not Found**
- Verify `templates/` folder is uploaded
- Check folder structure matches exactly

### **❌ Static Files Not Loading**
- Verify `static/` folder is uploaded
- Check `.htaccess` file is present

## 📞 **NEED HELP?**
Contact TrueHost support with:
1. Screenshot of error
2. File structure screenshot
3. Python app configuration details

## 🔧 **Environment Variables to Add:**
```
SECRET_KEY = liz-farm-production-secret-key-2024-secure-astrabyte-africa
FLASK_ENV = production
WHATSAPP_NUMBER = +254723812388
DATABASE_URL = postgresql://vxivdkko_lizfarm:LizFarm2024!@localhost/vxivdkko_lizfarm
```

## 🎉 **SUCCESS!**
Once deployed, your Liz Farm website will be live at:
**https://astrabyte.africa/lizfarm**

## 🎯 **Ready to Deploy!**
All your files are ready for TrueHost deployment. Just follow the steps above and your Liz Farm website will be live with:
- ✅ PostgreSQL database (no more localhost!)
- ✅ Job placement & training information
- ✅ Professional data storage
- ✅ All forms saving to database

**Need help? The deployment guides have troubleshooting sections for common issues.**
