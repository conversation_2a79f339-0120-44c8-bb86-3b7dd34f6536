<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Our Services - Siltech</title>
    <meta name="description" content="Explore all services offered by Siltech - Software development, IT consulting, cloud solutions, and tech partnership opportunities.">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'sans-serif'],
                        'serif': ['Playfair Display', 'serif'],
                    },
                    colors: {
                        'farm-green': {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#22c55e',
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d',
                        }
                    }
                }
            }
        }
    </script>

    <style>
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .animate-float {
            animation: float 3s ease-in-out infinite;
        }
    </style>
</head>
<body class="font-sans bg-gray-50">
    <!-- Navigation -->
    <nav class="fixed top-0 w-full z-50 bg-white shadow-lg" x-data="{ mobileMenuOpen: false }">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-3 sm:py-4">
                <!-- Logo -->
                <div class="flex items-center space-x-2">
                    <div class="w-7 h-7 sm:w-8 sm:h-8 bg-gradient-to-br from-farm-green-400 to-farm-green-600 rounded-full flex items-center justify-center animate-float">
                        <i class="fas fa-seedling text-white text-xs sm:text-sm"></i>
                    </div>
                    <div>
                        <h1 class="text-base sm:text-lg font-bold text-gray-900">Liz Farm</h1>
                        <p class="text-xs text-gray-600 hidden sm:block">The Oasis</p>
                    </div>
                </div>

                <!-- Desktop Navigation Links -->
                <div class="hidden md:flex items-center space-x-3 lg:space-x-4">
                    <a href="/" class="text-gray-700 hover:text-farm-green-600 transition-colors font-medium text-sm">Home</a>
                    <a href="/services" class="text-farm-green-600 font-medium text-sm">Services</a>
                    <a href="/#about" class="text-gray-700 hover:text-farm-green-600 transition-colors font-medium text-sm">About</a>
                    <a href="/#contact" class="text-gray-700 hover:text-farm-green-600 transition-colors font-medium text-sm">Contact</a>
                    <a href="/#contact" class="bg-farm-green-500 hover:bg-farm-green-600 text-white px-3 lg:px-4 py-2 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg font-semibold text-sm">
                        Get Started
                    </a>
                </div>

                <!-- Mobile Menu Button -->
                <button @click="mobileMenuOpen = !mobileMenuOpen" class="md:hidden p-2 rounded-lg transition-colors duration-300 relative z-50 text-gray-700 hover:bg-gray-100">
                    <i class="fas fa-bars text-lg" x-show="!mobileMenuOpen"></i>
                    <i class="fas fa-times text-lg" x-show="mobileMenuOpen"></i>
                </button>
            </div>


        </div>

        <!-- Mobile Menu Backdrop -->
        <div x-show="mobileMenuOpen"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             @click="mobileMenuOpen = false"
             class="md:hidden fixed inset-0 bg-black/50 backdrop-blur-sm z-30"></div>

        <!-- Mobile Menu -->
        <div x-show="mobileMenuOpen"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0 transform -translate-x-full"
             x-transition:enter-end="opacity-100 transform translate-x-0"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100 transform translate-x-0"
             x-transition:leave-end="opacity-0 transform -translate-x-full"
             class="md:hidden fixed inset-y-0 left-0 w-80 max-w-sm bg-gradient-to-b from-farm-green-600 to-farm-green-700 shadow-2xl z-40"
             style="padding-top: 20px;">
            <div class="px-4 py-4 space-y-4 h-full">
                <!-- Logo inside mobile menu at the top -->
                <div class="flex items-center space-x-2 mb-6 pb-3 border-b border-white/20">
                    <div class="w-8 h-8 bg-gradient-to-br from-farm-green-400 to-farm-green-300 rounded-full flex items-center justify-center">
                        <i class="fas fa-seedling text-white text-sm"></i>
                    </div>
                    <div>
                        <h1 class="text-sm font-bold text-white">Liz Farm</h1>
                        <p class="text-xs text-farm-green-100">The Oasis</p>
                    </div>
                </div>
                <a href="/" class="block text-sm font-medium text-white hover:text-farm-green-100 hover:bg-white/10 transition-all duration-300 py-2 px-2 rounded-lg border-b border-white/20" @click="mobileMenuOpen = false">
                    <i class="fas fa-home mr-2 text-farm-green-100 text-xs"></i>Home
                </a>
                <a href="/services" class="block text-sm font-medium text-white hover:text-farm-green-100 hover:bg-white/10 transition-all duration-300 py-2 px-2 rounded-lg border-b border-white/20" @click="mobileMenuOpen = false">
                    <i class="fas fa-seedling mr-2 text-farm-green-100 text-xs"></i>Services
                </a>
                <a href="/#about" class="block text-sm font-medium text-white hover:text-farm-green-100 hover:bg-white/10 transition-all duration-300 py-2 px-2 rounded-lg border-b border-white/20" @click="mobileMenuOpen = false">
                    <i class="fas fa-info-circle mr-2 text-farm-green-100 text-xs"></i>About
                </a>
                <a href="/#contact" class="block text-sm font-medium text-white hover:text-farm-green-100 hover:bg-white/10 transition-all duration-300 py-2 px-2 rounded-lg border-b border-white/20" @click="mobileMenuOpen = false">
                    <i class="fas fa-envelope mr-2 text-farm-green-100 text-xs"></i>Contact
                </a>
                <button @click="mobileMenuOpen = false; document.getElementById('contact') ? document.getElementById('contact').scrollIntoView({ behavior: 'smooth' }) : window.location.href='/#contact'"
                        class="w-full bg-white text-farm-green-700 hover:bg-farm-green-50 hover:text-farm-green-800 px-3 py-2 rounded-full transition-all duration-300 font-semibold text-sm shadow-lg mt-4 transform hover:scale-105">
                    <i class="fas fa-rocket mr-2"></i>Get Started
                </button>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="pt-16 sm:pt-20 pb-8 sm:pb-12 bg-gradient-to-br from-farm-green-600 via-farm-green-700 to-farm-green-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12 lg:py-16">
            <div class="text-center text-white">
                <h1 class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-4 sm:mb-6 font-serif leading-tight">Our Services</h1>
                <p class="text-lg sm:text-xl md:text-2xl mb-6 sm:mb-8 max-w-3xl mx-auto leading-relaxed px-4">
                    Comprehensive programs designed to empower individuals and communities through sustainable agriculture, global opportunities, and transformative experiences.
                </p>
                <div class="flex justify-center">
                    <a href="#services" class="bg-white text-farm-green-700 hover:bg-farm-green-50 px-6 sm:px-8 py-3 sm:py-4 rounded-full font-semibold text-base sm:text-lg transition-all duration-300 transform hover:scale-105 shadow-lg">
                        Explore Services
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="py-8 sm:py-12 lg:py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 mb-12 sm:mb-16">
                {% for service in services %}
                <div class="bg-white rounded-2xl p-6 sm:p-8 shadow-lg hover:shadow-xl transition-shadow duration-300" x-data="{ expanded: false }">
                    <!-- Mobile-first header layout -->
                    <div class="mb-6">
                        <!-- Service icon and title -->
                        <div class="flex flex-col sm:flex-row sm:items-start sm:justify-between mb-4">
                            <div class="flex items-center mb-4 sm:mb-0">
                                <div class="w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-{{ service.color }}-400 to-{{ service.color }}-600 rounded-full flex items-center justify-center mr-3 sm:mr-4 flex-shrink-0">
                                    <i class="{{ service.icon }} text-white text-lg sm:text-2xl"></i>
                                </div>
                                <div class="min-w-0 flex-1">
                                    <h3 class="text-xl sm:text-2xl font-bold text-gray-900 mb-1 leading-tight">{{ service.title }}</h3>
                                    <p class="text-{{ service.color }}-600 font-medium text-sm sm:text-base">{{ service.subtitle }}</p>
                                </div>
                            </div>

                            <!-- Price section -->
                            <div class="text-left sm:text-right flex-shrink-0">
                                <p class="text-xl sm:text-2xl font-bold text-gray-900">
                                    {% if service.price > 0 %}
                                        KES {{ "{:,}".format(service.price) }}
                                    {% else %}
                                        Free
                                    {% endif %}
                                </p>
                                <p class="text-sm text-gray-600">{{ service.duration }}</p>
                            </div>
                        </div>
                    </div>

                    <p class="text-gray-600 mb-6">{{ service.description }}</p>

                    <div class="space-y-3 mb-6">
                        {% for feature in service.features %}
                        <div class="flex items-center text-gray-700">
                            <i class="fas fa-check-circle text-{{ service.color }}-500 mr-3 flex-shrink-0"></i>
                            <span>{{ feature }}</span>
                        </div>
                        {% endfor %}
                    </div>

                    <div class="bg-{{ service.color }}-50 rounded-lg p-4 mb-6">
                        <p class="text-{{ service.color }}-800 font-medium">
                            <i class="fas fa-certificate mr-2"></i>
                            {{ service.certification }}
                        </p>
                    </div>

                    {% if service.expandable_details %}
                    <!-- Expandable Details -->
                    <div x-show="expanded" x-transition class="mb-6 p-4 bg-gray-50 rounded-lg border-l-4 border-{{ service.color }}-500">
                        <h4 class="font-semibold text-gray-800 mb-3 text-sm sm:text-base">Program Details:</h4>
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3 text-xs sm:text-sm text-gray-700">
                            {% for key, value in service.expandable_details.items() %}
                            <div class="flex items-start sm:items-center">
                                <i class="fas fa-{% if key == 'duration' %}clock{% elif key == 'schedule' %}calendar{% elif key == 'certification' %}award{% else %}hands-helping{% endif %} text-{{ service.color }}-500 mr-2 mt-1 sm:mt-0 flex-shrink-0"></i>
                                <span class="leading-tight"><strong>{{ key.title() }}:</strong> {{ value }}</span>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <div class="flex gap-3 mb-6">
                        <button @click="expanded = !expanded"
                                class="text-{{ service.color }}-600 hover:text-{{ service.color }}-700 text-sm font-medium transition-all duration-300 flex items-center">
                            <span x-text="expanded ? 'Show Less' : 'Learn More'"></span>
                            <i class="fas fa-chevron-down ml-1 transition-transform duration-300" :class="expanded ? 'rotate-180' : ''"></i>
                        </button>
                    </div>
                    {% endif %}

                    <!-- Action buttons - mobile responsive -->
                    <div class="space-y-3 sm:space-y-0">
                        {% if service.has_dual_buttons %}
                        <!-- Dual buttons layout -->
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-3 sm:mb-0">
                            {% for button in service.dual_buttons %}
                            <button onclick="openWhatsApp('{{ service.title }} - {{ button.text }}', '{{ service.title }}')"
                                    class="bg-{{ service.color }}-500 hover:bg-{{ service.color }}-600 text-white py-3 px-4 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 text-sm sm:text-base">
                                {{ button.text }}
                                <span class="block text-xs mt-1">KES {{ "{:,}".format(button.price) }}</span>
                            </button>
                            {% endfor %}
                        </div>

                        <!-- WhatsApp button for dual button services -->
                        <div class="flex justify-center sm:hidden">
                            <button onclick="openWhatsApp('{{ service.title }}', '{{ service.title }}')"
                                    class="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 flex items-center">
                                <i class="fab fa-whatsapp text-lg mr-2"></i>
                                WhatsApp Us
                            </button>
                        </div>

                        {% else %}
                        <!-- Single button layout -->
                        <div class="flex flex-col sm:flex-row gap-3">
                            <button onclick="openWhatsApp('{{ service.title }}', '{{ service.title }}')"
                                    class="flex-1 bg-{{ service.color }}-500 hover:bg-{{ service.color }}-600 text-white py-3 px-4 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 text-sm sm:text-base">
                                <i class="fas fa-arrow-right mr-2"></i>
                                {{ service.button_text }}
                            </button>

                            <!-- WhatsApp Button -->
                            <button onclick="openWhatsApp('{{ service.title }}', '{{ service.title }}')"
                                    class="bg-green-500 hover:bg-green-600 text-white px-4 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 sm:w-auto">
                                <i class="fab fa-whatsapp text-lg sm:text-xl"></i>
                                <span class="ml-2 sm:hidden">WhatsApp</span>
                            </button>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>

            <!-- Call to Action -->
            <div class="text-center bg-white rounded-2xl p-6 sm:p-8 shadow-lg">
                <h2 class="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">Ready to Get Started?</h2>
                <p class="text-lg sm:text-xl text-gray-600 mb-6 sm:mb-8 px-4">Contact us today to learn more about our services and how we can help you achieve your goals.</p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center max-w-md sm:max-w-none mx-auto">
                    <a href="/#contact" class="bg-farm-green-500 hover:bg-farm-green-600 text-white px-6 sm:px-8 py-3 sm:py-4 rounded-lg font-semibold text-base sm:text-lg transition-all duration-300 transform hover:scale-105">
                        <i class="fas fa-envelope mr-2"></i>
                        Contact Us
                    </a>
                    <button onclick="openWhatsApp('General Inquiry', 'I would like to learn more about your services')"
                            class="bg-green-500 hover:bg-green-600 text-white px-6 sm:px-8 py-3 sm:py-4 rounded-lg font-semibold text-base sm:text-lg transition-all duration-300 transform hover:scale-105">
                        <i class="fab fa-whatsapp mr-2"></i>
                        WhatsApp Us
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Job Placement & Training Information Section -->
    <section id="job-info" class="py-12 sm:py-16 md:py-20 bg-gradient-to-br from-farm-green-50 to-farm-green-100">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12 sm:mb-16 intersection-observer">
                <h2 class="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 font-serif px-4">
                    🌍 Job Placement & Training Information
                </h2>
                <p class="text-base sm:text-lg md:text-xl text-gray-600 max-w-3xl mx-auto px-4">
                    Comprehensive training programs and job placement services to launch your agricultural career
                </p>
            </div>

            <div class="grid lg:grid-cols-3 gap-8 mb-12">
                <!-- Registration Fees -->
                <div class="bg-white rounded-2xl p-6 sm:p-8 shadow-lg intersection-observer">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-farm-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-clipboard-list text-2xl text-farm-green-600"></i>
                        </div>
                        <h3 class="text-xl sm:text-2xl font-bold text-gray-900 mb-2">💼 Registration Fees</h3>
                        <p class="text-gray-600 text-sm">Mandatory for All Applicants</p>
                    </div>

                    <div class="space-y-4">
                        <div class="bg-farm-green-50 rounded-lg p-4">
                            <div class="flex justify-between items-center">
                                <span class="font-semibold text-gray-900">Local Jobs</span>
                                <span class="text-2xl font-bold text-farm-green-600">KES 2,000</span>
                            </div>
                        </div>
                        <div class="bg-blue-50 rounded-lg p-4">
                            <div class="flex justify-between items-center">
                                <span class="font-semibold text-gray-900">International Jobs</span>
                                <span class="text-2xl font-bold text-blue-600">KES 6,500</span>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 p-4 bg-yellow-50 rounded-lg border-l-4 border-yellow-400">
                        <p class="text-sm text-yellow-800">
                            <i class="fas fa-info-circle mr-2"></i>
                            Registration is required before proceeding to training, interviews, or job placement
                        </p>
                    </div>
                </div>

                <!-- Training Categories -->
                <div class="bg-white rounded-2xl p-6 sm:p-8 shadow-lg intersection-observer">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-graduation-cap text-2xl text-blue-600"></i>
                        </div>
                        <h3 class="text-xl sm:text-2xl font-bold text-gray-900 mb-2">🧠 Training Categories</h3>
                        <p class="text-gray-600 text-sm">Choose Your Path</p>
                    </div>

                    <div class="space-y-6">
                        <div class="border-l-4 border-farm-green-500 pl-4">
                            <h4 class="font-bold text-gray-900 mb-2">1. Already Trained / With Experience</h4>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>• Eligible for free refresher courses</li>
                                <li>• Interview and job placement preparation</li>
                                <li>• Duration: A few days</li>
                                <li>• Local or international opportunities</li>
                            </ul>
                        </div>

                        <div class="border-l-4 border-blue-500 pl-4">
                            <h4 class="font-bold text-gray-900 mb-2">2. Untrained / Without Experience</h4>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>• Full training and job preparation required</li>
                                <li>• Skills training and certification</li>
                                <li>• Job connection services</li>
                                <li>• Comprehensive career support</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Training & Placement Fees -->
                <div class="bg-white rounded-2xl p-6 sm:p-8 shadow-lg intersection-observer">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-dollar-sign text-2xl text-purple-600"></i>
                        </div>
                        <h3 class="text-xl sm:text-2xl font-bold text-gray-900 mb-2">💰 Training & Placement Fees</h3>
                        <p class="text-gray-600 text-sm">Investment in Your Future</p>
                    </div>

                    <div class="space-y-4">
                        <div class="bg-farm-green-50 rounded-lg p-4">
                            <div class="text-center">
                                <span class="block font-semibold text-gray-900 mb-1">Local Jobs</span>
                                <span class="text-2xl font-bold text-farm-green-600">From KES 26,000</span>
                            </div>
                        </div>
                        <div class="bg-blue-50 rounded-lg p-4">
                            <div class="text-center">
                                <span class="block font-semibold text-gray-900 mb-1">International Jobs</span>
                                <span class="text-2xl font-bold text-blue-600">From KES 65,000</span>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 p-4 bg-green-50 rounded-lg border-l-4 border-green-400">
                        <p class="text-sm text-green-800">
                            <i class="fas fa-check-circle mr-2"></i>
                            Includes comprehensive training, certification, and job placement support
                        </p>
                    </div>
                </div>
            </div>

            <!-- Important Note -->
            <div class="bg-gradient-to-r from-farm-green-600 to-farm-green-700 rounded-2xl p-6 sm:p-8 text-white text-center intersection-observer">
                <div class="max-w-4xl mx-auto">
                    <h3 class="text-xl sm:text-2xl font-bold mb-4">✅ Important Note</h3>
                    <p class="text-lg leading-relaxed">
                        All applicants must first register by paying the registration fee before proceeding to training,
                        interviews, or job connection services. This ensures serious commitment and helps us provide
                        personalized career guidance.
                    </p>
                    <div class="mt-6 flex flex-col sm:flex-row gap-4 justify-center">
                        <button onclick="openWhatsApp('Registration Inquiry', 'I would like to register for job placement services')"
                                class="bg-white text-farm-green-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-all duration-300">
                            <i class="fab fa-whatsapp mr-2"></i>
                            Register Now
                        </button>
                        <button onclick="openWhatsApp('Training Inquiry', 'I need more information about training programs')"
                                class="bg-farm-green-500 hover:bg-farm-green-400 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300">
                            <i class="fas fa-info-circle mr-2"></i>
                            Learn More
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Job Placement & Training Information Section -->
    <section id="job-info" class="py-12 sm:py-16 md:py-20 bg-gradient-to-br from-farm-green-50 to-farm-green-100">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12 sm:mb-16">
                <h2 class="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 font-serif px-4">
                    🌍 Job Placement & Training Information
                </h2>
                <p class="text-base sm:text-lg md:text-xl text-gray-600 max-w-3xl mx-auto px-4">
                    Comprehensive training programs and job placement services to launch your agricultural career
                </p>
            </div>

            <div class="grid lg:grid-cols-3 gap-8 mb-12">
                <!-- Registration Fees -->
                <div class="bg-white rounded-2xl p-6 sm:p-8 shadow-lg">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-farm-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-clipboard-list text-2xl text-farm-green-600"></i>
                        </div>
                        <h3 class="text-xl sm:text-2xl font-bold text-gray-900 mb-2">💼 Registration Fees</h3>
                        <p class="text-gray-600 text-sm">Mandatory for All Applicants</p>
                    </div>

                    <div class="space-y-4">
                        <div class="bg-farm-green-50 rounded-lg p-4">
                            <div class="flex justify-between items-center">
                                <span class="font-semibold text-gray-900">Local Jobs</span>
                                <span class="text-2xl font-bold text-farm-green-600">KES 2,000</span>
                            </div>
                        </div>
                        <div class="bg-blue-50 rounded-lg p-4">
                            <div class="flex justify-between items-center">
                                <span class="font-semibold text-gray-900">International Jobs</span>
                                <span class="text-2xl font-bold text-blue-600">KES 6,500</span>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 p-4 bg-yellow-50 rounded-lg border-l-4 border-yellow-400">
                        <p class="text-sm text-yellow-800">
                            <i class="fas fa-info-circle mr-2"></i>
                            Registration is required before proceeding to training, interviews, or job placement
                        </p>
                    </div>
                </div>

                <!-- Training Categories -->
                <div class="bg-white rounded-2xl p-6 sm:p-8 shadow-lg">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-graduation-cap text-2xl text-blue-600"></i>
                        </div>
                        <h3 class="text-xl sm:text-2xl font-bold text-gray-900 mb-2">🧠 Training Categories</h3>
                        <p class="text-gray-600 text-sm">Choose Your Path</p>
                    </div>

                    <div class="space-y-6">
                        <div class="border-l-4 border-farm-green-500 pl-4">
                            <h4 class="font-bold text-gray-900 mb-2">1. Already Trained / With Experience</h4>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>• Eligible for free refresher courses</li>
                                <li>• Interview and job placement preparation</li>
                                <li>• Duration: A few days</li>
                                <li>• Local or international opportunities</li>
                            </ul>
                        </div>

                        <div class="border-l-4 border-blue-500 pl-4">
                            <h4 class="font-bold text-gray-900 mb-2">2. Untrained / Without Experience</h4>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>• Full training and job preparation required</li>
                                <li>• Skills training and certification</li>
                                <li>• Job connection services</li>
                                <li>• Comprehensive career support</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Training & Placement Fees -->
                <div class="bg-white rounded-2xl p-6 sm:p-8 shadow-lg">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-dollar-sign text-2xl text-purple-600"></i>
                        </div>
                        <h3 class="text-xl sm:text-2xl font-bold text-gray-900 mb-2">💰 Training & Placement Fees</h3>
                        <p class="text-gray-600 text-sm">Investment in Your Future</p>
                    </div>

                    <div class="space-y-4">
                        <div class="bg-farm-green-50 rounded-lg p-4">
                            <div class="text-center">
                                <span class="block font-semibold text-gray-900 mb-1">Local Jobs</span>
                                <span class="text-2xl font-bold text-farm-green-600">From KES 26,000</span>
                            </div>
                        </div>
                        <div class="bg-blue-50 rounded-lg p-4">
                            <div class="text-center">
                                <span class="block font-semibold text-gray-900 mb-1">International Jobs</span>
                                <span class="text-2xl font-bold text-blue-600">From KES 65,000</span>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 p-4 bg-green-50 rounded-lg border-l-4 border-green-400">
                        <p class="text-sm text-green-800">
                            <i class="fas fa-check-circle mr-2"></i>
                            Includes comprehensive training, certification, and job placement support
                        </p>
                    </div>
                </div>
            </div>

            <!-- Important Note -->
            <div class="bg-gradient-to-r from-farm-green-600 to-farm-green-700 rounded-2xl p-6 sm:p-8 text-white text-center">
                <div class="max-w-4xl mx-auto">
                    <h3 class="text-xl sm:text-2xl font-bold mb-4">✅ Important Note</h3>
                    <p class="text-lg leading-relaxed">
                        All applicants must first register by paying the registration fee before proceeding to training,
                        interviews, or job connection services. This ensures serious commitment and helps us provide
                        personalized career guidance.
                    </p>
                    <div class="mt-6 flex flex-col sm:flex-row gap-4 justify-center">
                        <button onclick="openWhatsApp('Registration Inquiry', 'I would like to register for job placement services')"
                                class="bg-white text-farm-green-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-all duration-300">
                            <i class="fab fa-whatsapp mr-2"></i>
                            Register Now
                        </button>
                        <button onclick="openWhatsApp('Training Inquiry', 'I need more information about training programs')"
                                class="bg-farm-green-500 hover:bg-farm-green-400 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300">
                            <i class="fas fa-info-circle mr-2"></i>
                            Learn More
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12 sm:py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid sm:grid-cols-2 lg:grid-cols-4 gap-8 mb-8 sm:mb-12">
                <!-- Company Info -->
                <div class="sm:col-span-2 lg:col-span-2">
                    <div class="flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-3 mb-6">
                        <div class="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-tech-blue-400 to-tech-blue-600 rounded-full flex items-center justify-center animate-float">
                            <i class="fas fa-microchip text-white text-lg sm:text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-xl sm:text-2xl font-bold">Siltech</h3>
                            <p class="text-sm sm:text-base text-gray-400">Where Innovation, Technology & Success Converge</p>
                        </div>
                    </div>
                    <p class="text-sm sm:text-base text-gray-300 mb-6 max-w-md">
                        Building an ecosystem of empowerment through sustainable agriculture,
                        global job placements, and transformative experiences in Jaika Hill Estate, Kenya.
                    </p>
                    <div class="flex space-x-3 sm:space-x-4">
                        <a href="#" class="w-8 h-8 sm:w-10 sm:h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-farm-green-600 transition-colors duration-300">
                            <i class="fab fa-facebook-f text-sm sm:text-base"></i>
                        </a>
                        <a href="#" class="w-8 h-8 sm:w-10 sm:h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-farm-green-600 transition-colors duration-300">
                            <i class="fab fa-twitter text-sm sm:text-base"></i>
                        </a>
                        <a href="#" class="w-8 h-8 sm:w-10 sm:h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-farm-green-600 transition-colors duration-300">
                            <i class="fab fa-instagram text-sm sm:text-base"></i>
                        </a>
                        <a href="#" class="w-8 h-8 sm:w-10 sm:h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-farm-green-600 transition-colors duration-300">
                            <i class="fab fa-linkedin-in text-sm sm:text-base"></i>
                        </a>
                        <a href="https://wa.me/254700000000" class="w-8 h-8 sm:w-10 sm:h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-green-600 transition-colors duration-300">
                            <i class="fab fa-whatsapp text-sm sm:text-base"></i>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="text-base sm:text-lg font-semibold mb-4 sm:mb-6">Quick Links</h4>
                    <ul class="space-y-2 sm:space-y-3">
                        <li><a href="/" class="text-sm sm:text-base text-gray-300 hover:text-farm-green-400 transition-colors duration-300">Home</a></li>
                        <li><a href="/services" class="text-sm sm:text-base text-farm-green-400 font-medium">Services</a></li>
                        <li><a href="/#about" class="text-sm sm:text-base text-gray-300 hover:text-farm-green-400 transition-colors duration-300">About</a></li>
                        <li><a href="/#contact" class="text-sm sm:text-base text-gray-300 hover:text-farm-green-400 transition-colors duration-300">Contact</a></li>
                    </ul>
                </div>

                <!-- Services -->
                <div>
                    <h4 class="text-base sm:text-lg font-semibold mb-4 sm:mb-6">Our Services</h4>
                    <ul class="space-y-2 sm:space-y-3">
                        <li><a href="#services" class="text-sm sm:text-base text-gray-300 hover:text-farm-green-400 transition-colors duration-300">Agricultural Training</a></li>
                        <li><a href="#services" class="text-sm sm:text-base text-gray-300 hover:text-farm-green-400 transition-colors duration-300">Job Placement</a></li>
                        <li><a href="#services" class="text-sm sm:text-base text-gray-300 hover:text-farm-green-400 transition-colors duration-300">Consultancy</a></li>
                        <li><a href="#services" class="text-sm sm:text-base text-gray-300 hover:text-farm-green-400 transition-colors duration-300">Training Programs</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 pt-6 sm:pt-8 text-center">
                <p class="text-xs sm:text-sm text-gray-400 px-4">
                    © 2024 Siltech. All rights reserved. |
                    <a href="#" class="hover:text-tech-blue-400 transition-colors duration-300">Privacy Policy</a> |
                    <a href="#" class="hover:text-tech-blue-400 transition-colors duration-300">Terms of Service</a>
                </p>
            </div>
        </div>
    </footer>

    <script>
        function openWhatsApp(service, defaultMessage) {
            const message = defaultMessage || `I am interested in ${service}. Please provide more information.`;
            const whatsappNumber = "254700000000";
            const encodedMessage = encodeURIComponent(message);
            const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${encodedMessage}`;
            window.open(whatsappUrl, '_blank');
        }
    </script>
</body>
</html>
