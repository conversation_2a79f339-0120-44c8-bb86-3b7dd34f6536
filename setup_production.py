#!/usr/bin/env python3
"""
Production setup script for Liz Farm website
Run this after uploading to TrueHost to verify everything is working
"""

import os
import sys

def check_files():
    """Check if all required files are present"""
    required_files = [
        'app.py',
        'wsgi.py',
        'requirements.txt',
        '.htaccess',
        'templates/index.html',
        'templates/services.html',
        'templates/admin/dashboard.html',
        'templates/admin/login.html'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ Missing files:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    else:
        print("✅ All required files present")
        return True

def check_python_version():
    """Check Python version compatibility"""
    version = sys.version_info
    if version.major >= 3 and version.minor >= 7:
        print(f"✅ Python version: {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print(f"❌ Python version {version.major}.{version.minor}.{version.micro} may not be compatible")
        print("   Recommended: Python 3.7 or higher")
        return False

def check_dependencies():
    """Check if required packages can be imported"""
    required_packages = [
        'flask',
        'flask_cors'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n💡 Run: pip install -r requirements.txt")
        return False
    else:
        print("✅ All required packages available")
        return True

def test_app_import():
    """Test if the Flask app can be imported"""
    try:
        from app import app
        print("✅ Flask app imports successfully")
        return True
    except Exception as e:
        print(f"❌ Error importing Flask app: {e}")
        return False

def generate_secret_key():
    """Generate a secure secret key for production"""
    import secrets
    key = secrets.token_hex(32)
    print(f"🔐 Generated secret key: {key}")
    print("   Add this to your environment variables as SECRET_KEY")
    return key

def main():
    """Run all checks"""
    print("🚀 Liz Farm - Production Setup Check")
    print("=" * 40)
    
    checks = [
        ("Files", check_files),
        ("Python Version", check_python_version),
        ("Dependencies", check_dependencies),
        ("App Import", test_app_import)
    ]
    
    all_passed = True
    for name, check_func in checks:
        print(f"\n📋 Checking {name}...")
        if not check_func():
            all_passed = False
    
    print("\n" + "=" * 40)
    
    if all_passed:
        print("🎉 All checks passed! Your app is ready for production.")
        print("\n🔐 Security Recommendations:")
        generate_secret_key()
        print("   - Change admin password after first login")
        print("   - Enable HTTPS/SSL certificate")
        print("   - Set FLASK_ENV=production")
    else:
        print("⚠️  Some checks failed. Please fix the issues above.")
    
    print("\n📚 Next Steps:")
    print("1. Upload files to TrueHost public_html folder")
    print("2. Set up Python App in cPanel")
    print("3. Install dependencies: pip install -r requirements.txt")
    print("4. Configure environment variables")
    print("5. Test your website")
    
    print("\n📖 For detailed instructions, see DEPLOYMENT_GUIDE.md")

if __name__ == '__main__':
    main()
