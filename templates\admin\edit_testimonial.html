<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Testimonial - <PERSON> Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'farm-green': {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#22c55e',
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-br from-farm-green-400 to-farm-green-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-seedling text-white"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-900">Liz Farm Admin</h1>
                        <p class="text-sm text-gray-600">Edit Testimonial</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="{{ url_for('admin_testimonials') }}" class="text-gray-600 hover:text-farm-green-600 transition-colors">
                        <i class="fas fa-arrow-left mr-1"></i>
                        Back to Testimonials
                    </a>
                    <a href="{{ url_for('admin_logout') }}" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-sign-out-alt mr-1"></i>
                        Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="mb-6 p-4 rounded-lg {% if category == 'error' %}bg-red-50 text-red-800 border border-red-200{% else %}bg-green-50 text-green-800 border border-green-200{% endif %}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Edit Testimonial</h1>
            <p class="text-gray-600 mt-2">Update the testimonial details</p>
        </div>

        <!-- Form -->
        <div class="bg-white rounded-xl shadow-lg" x-data="testimonialForm()">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-900">Testimonial Details</h2>
            </div>

            <form method="POST" class="p-6">
                <div class="grid md:grid-cols-2 gap-6">
                    <!-- Customer Information -->
                    <div class="space-y-6">
                        <h3 class="text-lg font-semibold text-gray-900">Customer Information</h3>
                        
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                                Customer Name *
                            </label>
                            <input type="text" 
                                   id="name" 
                                   name="name" 
                                   required
                                   value="{{ testimonial.name }}"
                                   x-model="form.name"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-farm-green-500 focus:border-farm-green-500 transition-colors">
                        </div>

                        <div>
                            <label for="role" class="block text-sm font-medium text-gray-700 mb-2">
                                Role/Title *
                            </label>
                            <input type="text" 
                                   id="role" 
                                   name="role" 
                                   required
                                   value="{{ testimonial.role }}"
                                   x-model="form.role"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-farm-green-500 focus:border-farm-green-500 transition-colors">
                        </div>

                        <div>
                            <label for="location" class="block text-sm font-medium text-gray-700 mb-2">
                                Location *
                            </label>
                            <input type="text" 
                                   id="location" 
                                   name="location" 
                                   required
                                   value="{{ testimonial.location }}"
                                   x-model="form.location"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-farm-green-500 focus:border-farm-green-500 transition-colors">
                        </div>

                        <div>
                            <label for="icon" class="block text-sm font-medium text-gray-700 mb-2">
                                Icon *
                            </label>
                            <select id="icon"
                                    name="icon"
                                    required
                                    x-model="form.icon"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-farm-green-500 focus:border-farm-green-500 transition-colors">
                                <option value="fas fa-user" {% if testimonial.icon == 'fas fa-user' %}selected{% endif %}>User</option>
                                <option value="fas fa-seedling" {% if testimonial.icon == 'fas fa-seedling' %}selected{% endif %}>Seedling</option>
                                <option value="fas fa-briefcase" {% if testimonial.icon == 'fas fa-briefcase' %}selected{% endif %}>Briefcase</option>
                                <option value="fas fa-camera" {% if testimonial.icon == 'fas fa-camera' %}selected{% endif %}>Camera</option>
                                <option value="fas fa-handshake" {% if testimonial.icon == 'fas fa-handshake' %}selected{% endif %}>Handshake</option>
                                <option value="fas fa-graduation-cap" {% if testimonial.icon == 'fas fa-graduation-cap' %}selected{% endif %}>Graduation Cap</option>
                                <option value="fas fa-globe" {% if testimonial.icon == 'fas fa-globe' %}selected{% endif %}>Globe</option>
                                <option value="fas fa-star" {% if testimonial.icon == 'fas fa-star' %}selected{% endif %}>Star</option>
                                <option value="fas fa-heart" {% if testimonial.icon == 'fas fa-heart' %}selected{% endif %}>Heart</option>
                                <option value="fas fa-trophy" {% if testimonial.icon == 'fas fa-trophy' %}selected{% endif %}>Trophy</option>
                            </select>
                        </div>

                        <div>
                            <label for="color" class="block text-sm font-medium text-gray-700 mb-2">
                                Color Theme *
                            </label>
                            <select id="color"
                                    name="color"
                                    required
                                    x-model="form.color"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-farm-green-500 focus:border-farm-green-500 transition-colors">
                                <option value="farm-green" {% if testimonial.color == 'farm-green' %}selected{% endif %}>Farm Green</option>
                                <option value="blue" {% if testimonial.color == 'blue' %}selected{% endif %}>Blue</option>
                                <option value="green" {% if testimonial.color == 'green' %}selected{% endif %}>Green</option>
                                <option value="purple" {% if testimonial.color == 'purple' %}selected{% endif %}>Purple</option>
                                <option value="indigo" {% if testimonial.color == 'indigo' %}selected{% endif %}>Indigo</option>
                                <option value="teal" {% if testimonial.color == 'teal' %}selected{% endif %}>Teal</option>
                                <option value="red" {% if testimonial.color == 'red' %}selected{% endif %}>Red</option>
                                <option value="yellow" {% if testimonial.color == 'yellow' %}selected{% endif %}>Yellow</option>
                                <option value="pink" {% if testimonial.color == 'pink' %}selected{% endif %}>Pink</option>
                                <option value="orange" {% if testimonial.color == 'orange' %}selected{% endif %}>Orange</option>
                            </select>
                        </div>
                    </div>

                    <!-- Testimonial Content -->
                    <div class="space-y-6">
                        <h3 class="text-lg font-semibold text-gray-900">Testimonial Content</h3>
                        
                        <div>
                            <label for="service" class="block text-sm font-medium text-gray-700 mb-2">
                                Service *
                            </label>
                            <select id="service" 
                                    name="service" 
                                    required
                                    x-model="form.service"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-farm-green-500 focus:border-farm-green-500 transition-colors">
                                {% for service in services %}
                                <option value="{{ service.title }}" {% if service.title == testimonial.service %}selected{% endif %}>{{ service.title }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <div>
                            <label for="rating" class="block text-sm font-medium text-gray-700 mb-2">
                                Rating *
                            </label>
                            <select id="rating" 
                                    name="rating" 
                                    required
                                    x-model="form.rating"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-farm-green-500 focus:border-farm-green-500 transition-colors">
                                <option value="5" {% if testimonial.rating == 5 %}selected{% endif %}>5 Stars - Excellent</option>
                                <option value="4" {% if testimonial.rating == 4 %}selected{% endif %}>4 Stars - Very Good</option>
                                <option value="3" {% if testimonial.rating == 3 %}selected{% endif %}>3 Stars - Good</option>
                                <option value="2" {% if testimonial.rating == 2 %}selected{% endif %}>2 Stars - Fair</option>
                                <option value="1" {% if testimonial.rating == 1 %}selected{% endif %}>1 Star - Poor</option>
                            </select>
                        </div>

                        <div>
                            <label for="date" class="block text-sm font-medium text-gray-700 mb-2">
                                Date *
                            </label>
                            <input type="date" 
                                   id="date" 
                                   name="date" 
                                   required
                                   value="{{ testimonial.date }}"
                                   x-model="form.date"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-farm-green-500 focus:border-farm-green-500 transition-colors">
                        </div>
                    </div>
                </div>

                <!-- Testimonial Text -->
                <div class="mt-6">
                    <label for="text" class="block text-sm font-medium text-gray-700 mb-2">
                        Testimonial Text *
                    </label>
                    <textarea id="text" 
                              name="text" 
                              rows="4" 
                              required
                              x-model="form.text"
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-farm-green-500 focus:border-farm-green-500 transition-colors">{{ testimonial.text }}</textarea>
                    <p class="text-sm text-gray-500 mt-1">Keep it authentic and specific about the results achieved</p>
                </div>

                <!-- Preview -->
                <div class="mt-8 p-6 bg-gray-50 rounded-lg">
                    <h3 class="font-semibold text-gray-900 mb-4">Live Preview</h3>
                    <div class="bg-white rounded-lg p-6 shadow">
                        <div class="flex items-center mb-4">
                            <template x-for="i in parseInt(form.rating || {{ testimonial.rating }})">
                                <i class="fas fa-star text-yellow-400 text-lg mr-1"></i>
                            </template>
                            <span class="text-sm text-gray-500 ml-2" x-text="`(${form.rating || {{ testimonial.rating }}}/5)`"></span>
                        </div>
                        
                        <blockquote class="text-gray-700 mb-6 leading-relaxed" x-text="`\"${form.text || '{{ testimonial.text }}'}\"`"></blockquote>
                        
                        <div class="text-center">
                            <div class="w-12 h-12 rounded-full mx-auto mb-3 flex items-center justify-center"
                                 :class="`bg-${form.color || '{{ testimonial.color }}'}-100`">
                                <i :class="form.icon || '{{ testimonial.icon }}'"
                                   :class="`text-${form.color || '{{ testimonial.color }}'}-600 text-lg`"></i>
                            </div>
                            <h4 class="font-semibold text-gray-900" x-text="form.name || '{{ testimonial.name }}'"></h4>
                            <p class="text-sm text-gray-600" x-text="form.role || '{{ testimonial.role }}'"></p>
                            <p class="text-xs text-gray-500" x-text="form.location || '{{ testimonial.location }}'"></p>
                        </div>
                        
                        <div class="mt-4 pt-4 border-t border-gray-100">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-farm-green-100 text-farm-green-800">
                                <i class="fas fa-check-circle mr-1"></i>
                                <span x-text="form.service || '{{ testimonial.service }}'"></span>
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200 mt-8">
                    <a href="{{ url_for('admin_testimonials') }}" 
                       class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="px-6 py-3 bg-farm-green-500 hover:bg-farm-green-600 text-white rounded-lg font-semibold transition-all duration-300 transform hover:scale-105">
                        <i class="fas fa-save mr-2"></i>
                        Update Testimonial
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function testimonialForm() {
            return {
                form: {
                    name: '{{ testimonial.name }}',
                    role: '{{ testimonial.role }}',
                    location: '{{ testimonial.location }}',
                    icon: '{{ testimonial.icon }}',
                    color: '{{ testimonial.color }}',
                    service: '{{ testimonial.service }}',
                    rating: '{{ testimonial.rating }}',
                    text: `{{ testimonial.text }}`,
                    date: '{{ testimonial.date }}'
                }
            }
        }
    </script>
</body>
</html>
