#!/usr/bin/env python3
"""
Liz Farm - Deployment Preparation Script
This script helps prepare your files for TrueHost deployment
"""

import os
import shutil
import zipfile
from datetime import datetime

def create_deployment_package():
    """Create a deployment package with all necessary files"""
    
    print("🌿 Preparing Liz Farm for TrueHost Deployment...")
    print("=" * 50)
    
    # Create deployment folder
    deploy_folder = "lizfarm_deployment"
    if os.path.exists(deploy_folder):
        shutil.rmtree(deploy_folder)
    os.makedirs(deploy_folder)
    
    # Files to include
    files_to_copy = [
        'app.py',
        'wsgi.py',
        'requirements.txt',
        '.htaccess'
    ]
    
    # Folders to include
    folders_to_copy = [
        'templates',
        'static'
    ]
    
    print("📁 Copying files...")
    
    # Copy individual files
    for file in files_to_copy:
        if os.path.exists(file):
            shutil.copy2(file, deploy_folder)
            print(f"✅ Copied: {file}")
        else:
            print(f"❌ Missing: {file}")
    
    # Copy folders
    for folder in folders_to_copy:
        if os.path.exists(folder):
            shutil.copytree(folder, os.path.join(deploy_folder, folder))
            print(f"✅ Copied folder: {folder}")
        else:
            print(f"❌ Missing folder: {folder}")
    
    # Create deployment instructions
    instructions = """
# 🚀 TrueHost Deployment Instructions

## Upload these files to public_html/lizfarm/:

1. app.py
2. wsgi.py  
3. requirements.txt
4. .htaccess
5. templates/ (entire folder)
6. static/ (entire folder)

## Then:
1. Set up Python App in cPanel
2. Install dependencies: pip install -r requirements.txt
3. Set file permissions (755 for folders, 644 for files)
4. Visit: https://astrabyte.africa/lizfarm

## Support:
If you need help, contact TrueHost support.
"""
    
    with open(os.path.join(deploy_folder, "DEPLOYMENT_INSTRUCTIONS.txt"), "w") as f:
        f.write(instructions)
    
    print(f"\n🎉 Deployment package ready in: {deploy_folder}/")
    print("\n📋 Next steps:")
    print("1. Upload all files in the deployment folder to TrueHost")
    print("2. Follow the TRUEHOST_DEPLOYMENT.md guide")
    print("3. Set up Python App in cPanel")
    
    return deploy_folder

if __name__ == "__main__":
    create_deployment_package()
