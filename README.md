# 💻 Siltech – Tech Solutions Website

A beautiful, animated website for Siltech featuring modern web technologies and smooth user interactions.

## 🚀 Features

- **Beautiful Animations**: Smooth transitions and engaging animations using Tailwind CSS and Alpine.js
- **Responsive Design**: Fully responsive design that works perfectly on all devices
- **Interactive Forms**: Contact forms, job applications, and tour bookings with real-time validation
- **Modern UI/UX**: Clean, professional design with intuitive navigation
- **Backend Integration**: Flask backend with PostgreSQL database for data management
- **Email Notifications**: Automated email notifications for form submissions

## 🛠️ Tech Stack

### Frontend
- **HTML5**: Semantic markup
- **Tailwind CSS**: Utility-first CSS framework for styling and animations
- **Alpine.js**: Lightweight JavaScript framework for interactivity
- **Font Awesome**: Icons
- **Google Fonts**: Typography (Inter & Playfair Display)

### Backend
- **Python Flask**: Web framework
- **PostgreSQL**: Database
- **SQLAlchemy**: ORM
- **Flask-Mail**: Email functionality
- **Flask-Migrate**: Database migrations

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd lizfarm
   ```

2. **Create a virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your database and email configuration
   ```

5. **Set up PostgreSQL database**
   ```bash
   # Create database
   createdb lizfarm_db
   
   # Initialize database
   flask db init
   flask db migrate -m "Initial migration"
   flask db upgrade
   ```

6. **Run the application**
   ```bash
   python app.py
   ```

The website will be available at `http://localhost:5000`

## 🎨 Design Features

### Animations & Transitions
- **Fade-in animations** for content sections
- **Slide animations** for hero content
- **Hover effects** on cards and buttons
- **Floating animations** for decorative elements
- **Smooth scrolling** navigation
- **Parallax effects** on scroll
- **Loading animations** for form submissions

### Responsive Design
- **Mobile-first approach**
- **Flexible grid layouts**
- **Adaptive typography**
- **Touch-friendly interactions**
- **Optimized images and assets**

### Interactive Elements
- **Dynamic navigation** with scroll effects
- **Animated counters** in statistics section
- **Form validation** with real-time feedback
- **Newsletter subscription**
- **Smooth page transitions**

## 📱 Sections

1. **Hero Section**: Eye-catching introduction with call-to-action buttons
2. **Statistics**: Animated counters showing farm achievements
3. **Services**: Four main service offerings with detailed information
4. **About**: Why choose Liz Farm with feature highlights
5. **Contact**: Contact form and information
6. **Footer**: Links, social media, and newsletter signup

## 🔧 Configuration

### Database Configuration
Update the `DATABASE_URL` in your `.env` file:
```
DATABASE_URL=postgresql://username:password@localhost/lizfarm_db
```

### Email Configuration
Configure email settings in `.env`:
```
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
```

## 🚀 Quick Start

### For Development (Current Setup)
```bash
# 1. Install dependencies
pip install -r requirements.txt

# 2. Run the application
python app.py
# OR
python start_server.py

# 3. Open your browser to:
http://localhost:5000
```

### For Production Deployment

#### Option 1: Simple Production Setup
```bash
# 1. Install production dependencies
pip install gunicorn

# 2. Run with Gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

#### Option 2: Full Production Setup
1. **Set up environment variables**
   ```bash
   export FLASK_ENV=production
   export SECRET_KEY=your-production-secret-key-here
   ```

2. **Use a production WSGI server**
   ```bash
   pip install gunicorn
   gunicorn -w 4 -b 0.0.0.0:5000 app:app
   ```

3. **Set up reverse proxy with Nginx**
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;

       location / {
           proxy_pass http://127.0.0.1:5000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
       }
   }
   ```

4. **Configure SSL certificates** (recommended: Let's Encrypt)

#### Option 3: Deploy to Cloud Platforms

**Heroku:**
```bash
# Create Procfile
echo "web: gunicorn app:app" > Procfile

# Deploy
git add .
git commit -m "Deploy to Heroku"
git push heroku main
```

**Railway/Render/DigitalOcean:**
- Upload your code
- Set environment variables
- The platform will automatically detect Flask app

## 📞 Contact Information

- **Email**: <EMAIL>
- **Phone**: +254 723 812388
- **Location**: Jaika Hill Estate, Kenya

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

Built with ❤️ for Liz Farm – The Oasis
