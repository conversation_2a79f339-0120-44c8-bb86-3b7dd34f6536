<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Service - Liz Farm Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'farm-green': {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#22c55e',
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-br from-farm-green-400 to-farm-green-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-seedling text-white"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-900">Liz Farm Admin</h1>
                        <p class="text-sm text-gray-600">Edit Service</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="{{ url_for('admin_services') }}" class="text-gray-600 hover:text-farm-green-600 transition-colors">
                        <i class="fas fa-arrow-left mr-1"></i>
                        Back to Services
                    </a>
                    <a href="{{ url_for('admin_logout') }}" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-sign-out-alt mr-1"></i>
                        Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="bg-white rounded-xl shadow-lg">
            <div class="p-6 border-b border-gray-200">
                <h1 class="text-2xl font-bold text-gray-900">Edit Service</h1>
                <p class="text-gray-600 mt-2">Update the details for "{{ service.title }}"</p>
            </div>

            <form method="POST" class="p-6 space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Service Title *</label>
                        <input type="text" id="title" name="title" value="{{ service.title }}" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-farm-green-500 focus:border-farm-green-500 transition-colors">
                    </div>

                    <div>
                        <label for="subtitle" class="block text-sm font-medium text-gray-700 mb-2">Subtitle *</label>
                        <input type="text" id="subtitle" name="subtitle" value="{{ service.subtitle }}" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-farm-green-500 focus:border-farm-green-500 transition-colors">
                    </div>
                </div>

                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description *</label>
                    <textarea id="description" name="description" rows="4" required
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-farm-green-500 focus:border-farm-green-500 transition-colors">{{ service.description }}</textarea>
                </div>

                <div>
                    <label for="features" class="block text-sm font-medium text-gray-700 mb-2">Features (one per line) *</label>
                    <textarea id="features" name="features" rows="6" required
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-farm-green-500 focus:border-farm-green-500 transition-colors">{{ service.features|join('\n') }}</textarea>
                </div>

                <div>
                    <label for="certification" class="block text-sm font-medium text-gray-700 mb-2">Certification/Guarantee *</label>
                    <input type="text" id="certification" name="certification" value="{{ service.certification }}" required
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-farm-green-500 focus:border-farm-green-500 transition-colors">
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label for="price" class="block text-sm font-medium text-gray-700 mb-2">Price (KES) *</label>
                        <input type="number" id="price" name="price" value="{{ service.price }}" min="0" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-farm-green-500 focus:border-farm-green-500 transition-colors">
                    </div>

                    <div>
                        <label for="duration" class="block text-sm font-medium text-gray-700 mb-2">Duration *</label>
                        <input type="text" id="duration" name="duration" value="{{ service.duration }}" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-farm-green-500 focus:border-farm-green-500 transition-colors">
                    </div>

                    <div>
                        <label for="button_text" class="block text-sm font-medium text-gray-700 mb-2">Button Text *</label>
                        <input type="text" id="button_text" name="button_text" value="{{ service.button_text }}" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-farm-green-500 focus:border-farm-green-500 transition-colors">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="icon" class="block text-sm font-medium text-gray-700 mb-2">Icon Class *</label>
                        <select id="icon" name="icon" required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-farm-green-500 focus:border-farm-green-500 transition-colors">
                            <option value="fas fa-seedling" {% if service.icon == 'fas fa-seedling' %}selected{% endif %}>🌱 Seedling (Training)</option>
                            <option value="fas fa-briefcase" {% if service.icon == 'fas fa-briefcase' %}selected{% endif %}>💼 Briefcase (Jobs)</option>
                            <option value="fas fa-mountain" {% if service.icon == 'fas fa-mountain' %}selected{% endif %}>🏔️ Mountain (Tours)</option>
                            <option value="fas fa-handshake" {% if service.icon == 'fas fa-handshake' %}selected{% endif %}>🤝 Handshake (Partnership)</option>
                            <option value="fas fa-graduation-cap" {% if service.icon == 'fas fa-graduation-cap' %}selected{% endif %}>🎓 Graduation Cap (Education)</option>
                            <option value="fas fa-tools" {% if service.icon == 'fas fa-tools' %}selected{% endif %}>🔧 Tools (Technical)</option>
                            <option value="fas fa-leaf" {% if service.icon == 'fas fa-leaf' %}selected{% endif %}>🍃 Leaf (Organic)</option>
                            <option value="fas fa-tractor" {% if service.icon == 'fas fa-tractor' %}selected{% endif %}>🚜 Tractor (Farming)</option>
                        </select>
                    </div>

                    <div>
                        <label for="color" class="block text-sm font-medium text-gray-700 mb-2">Color Theme *</label>
                        <select id="color" name="color" required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-farm-green-500 focus:border-farm-green-500 transition-colors">
                            <option value="farm-green" {% if service.color == 'farm-green' %}selected{% endif %}>🟢 Farm Green</option>
                            <option value="blue" {% if service.color == 'blue' %}selected{% endif %}>🔵 Blue</option>
                            <option value="green" {% if service.color == 'green' %}selected{% endif %}>🟢 Green</option>
                            <option value="purple" {% if service.color == 'purple' %}selected{% endif %}>🟣 Purple</option>
                            <option value="red" {% if service.color == 'red' %}selected{% endif %}>🔴 Red</option>
                            <option value="yellow" {% if service.color == 'yellow' %}selected{% endif %}>🟡 Yellow</option>
                            <option value="indigo" {% if service.color == 'indigo' %}selected{% endif %}>🟦 Indigo</option>
                            <option value="pink" {% if service.color == 'pink' %}selected{% endif %}>🩷 Pink</option>
                        </select>
                    </div>
                </div>

                <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                    <a href="{{ url_for('admin_services') }}" 
                       class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="px-6 py-3 bg-farm-green-500 hover:bg-farm-green-600 text-white rounded-lg font-semibold transition-all duration-300 transform hover:scale-105">
                        <i class="fas fa-save mr-2"></i>
                        Update Service
                    </button>
                </div>
            </form>
        </div>
    </div>
</body>
</html>
