#!/usr/bin/env python3
"""
Backup script for Liz Farm website data
Run this script to export your services, stats, and testimonials data
"""

import json
from datetime import datetime
from app import services_data, stats_data, testimonials_data

def backup_data():
    """Create a backup of all website data"""
    
    # Create backup data structure
    backup = {
        'backup_date': datetime.now().isoformat(),
        'services': services_data,
        'stats': stats_data,
        'testimonials': testimonials_data
    }
    
    # Generate filename with timestamp
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f'lizfarm_backup_{timestamp}.json'
    
    # Save to file
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(backup, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Backup created successfully: {filename}")
        print(f"📊 Services: {len(services_data)} items")
        print(f"📈 Stats: {len(stats_data)} items")
        print(f"💬 Testimonials: {len(testimonials_data)} items")
        
    except Exception as e:
        print(f"❌ Error creating backup: {e}")

def restore_data(backup_file):
    """Restore data from backup file (for reference - manual implementation needed)"""
    
    try:
        with open(backup_file, 'r', encoding='utf-8') as f:
            backup = json.load(f)
        
        print(f"📅 Backup Date: {backup['backup_date']}")
        print(f"📊 Services: {len(backup['services'])} items")
        print(f"📈 Stats: {len(backup['stats'])} items")
        print(f"💬 Testimonials: {len(backup['testimonials'])} items")
        
        print("\n⚠️  To restore this data:")
        print("1. Copy the data from this backup file")
        print("2. Update the corresponding variables in app.py")
        print("3. Restart your Flask application")
        
        return backup
        
    except Exception as e:
        print(f"❌ Error reading backup: {e}")
        return None

if __name__ == '__main__':
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'restore':
        if len(sys.argv) > 2:
            restore_data(sys.argv[2])
        else:
            print("Usage: python backup_data.py restore <backup_file.json>")
    else:
        backup_data()
