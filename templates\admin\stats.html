<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Stats - Liz Farm Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'farm-green': {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#22c55e',
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-br from-farm-green-400 to-farm-green-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-seedling text-white"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-900">Liz Farm Admin</h1>
                        <p class="text-sm text-gray-600">Manage Statistics</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="{{ url_for('admin_dashboard') }}" class="text-gray-600 hover:text-farm-green-600 transition-colors">
                        <i class="fas fa-dashboard mr-1"></i>
                        Dashboard
                    </a>
                    <a href="{{ url_for('admin_logout') }}" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-sign-out-alt mr-1"></i>
                        Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="mb-6 p-4 rounded-lg {% if category == 'error' %}bg-red-50 text-red-800 border border-red-200{% else %}bg-green-50 text-green-800 border border-green-200{% endif %}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Manage Statistics</h1>
            <p class="text-gray-600 mt-2">Update the statistics displayed on your website homepage</p>
        </div>

        <!-- Stats Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {% for stat in stats %}
            <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-{{ stat.color }}-100 rounded-full flex items-center justify-center">
                            <i class="{{ stat.icon }} text-{{ stat.color }}-600 text-xl"></i>
                        </div>
                        <div class="text-right">
                            <p class="text-3xl font-bold text-{{ stat.color }}-600">{{ stat.value }}+</p>
                        </div>
                    </div>

                    <h3 class="text-lg font-semibold text-gray-900 mb-4">{{ stat.title }}</h3>

                    <div class="space-y-2 text-sm text-gray-600 mb-4">
                        <div class="flex items-center">
                            <i class="fas fa-palette text-gray-400 mr-2"></i>
                            <span>Color: {{ stat.color.title() }}</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-icons text-gray-400 mr-2"></i>
                            <span>Icon: {{ stat.icon }}</span>
                        </div>
                    </div>

                    <a href="{{ url_for('admin_edit_stat', stat_id=stat.id) }}" 
                       class="w-full bg-{{ stat.color }}-500 hover:bg-{{ stat.color }}-600 text-white py-2 px-4 rounded-lg font-medium transition-colors text-center block">
                        <i class="fas fa-edit mr-1"></i>
                        Edit Statistic
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Preview Section -->
        <div class="mt-12 bg-white rounded-xl shadow-lg">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-900">Live Preview</h2>
                <p class="text-gray-600 mt-1">This is how your statistics will appear on the website</p>
            </div>
            <div class="p-8">
                <div class="grid grid-cols-2 lg:grid-cols-4 gap-6">
                    {% for stat in stats %}
                    <div class="text-center p-4">
                        <div class="flex items-center justify-center mb-3">
                            <div class="w-12 h-12 bg-{{ stat.color }}-100 rounded-full flex items-center justify-center">
                                <i class="{{ stat.icon }} text-{{ stat.color }}-600 text-xl"></i>
                            </div>
                        </div>
                        <div class="text-2xl sm:text-3xl md:text-4xl font-bold text-{{ stat.color }}-600 mb-2">
                            {{ stat.value }}+
                        </div>
                        <p class="text-sm sm:text-base text-gray-600 font-medium">{{ stat.title }}</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="mt-8 bg-blue-50 rounded-xl p-6">
            <div class="flex items-start">
                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-4 mt-1">
                    <i class="fas fa-info text-blue-600"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-blue-900 mb-2">How to Update Statistics</h3>
                    <ul class="text-blue-800 space-y-1 text-sm">
                        <li>• Click "Edit Statistic" on any stat card to modify its value, title, icon, or color</li>
                        <li>• Changes will be reflected immediately on your website</li>
                        <li>• The counter animation will automatically adjust to your new values</li>
                        <li>• Choose colors and icons that match your brand and service offerings</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
